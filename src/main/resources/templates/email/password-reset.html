<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset Request</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #dc2626;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background-color: #fef2f2;
            padding: 30px;
            border-radius: 0 0 8px 8px;
        }
        .button {
            display: inline-block;
            background-color: #dc2626;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 4px;
            margin: 20px 0;
        }
        .security-notice {
            background-color: #fee2e2;
            border: 1px solid #dc2626;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔐 Password Reset Request</h1>
    </div>
    
    <div class="content">
        <h2>Hello <span th:text="${userName}">User</span>,</h2>
        
        <p>We received a request to reset your password for your <span th:text="${companyName}">Arihant Capital</span> account.</p>
        
        <p>If you made this request, click the button below to reset your password:</p>
        
        <a th:href="${resetUrl}" class="button">Reset Your Password</a>
        
        <p>Or copy and paste this link into your browser:</p>
        <p style="word-break: break-all; background-color: #f3f4f6; padding: 10px; border-radius: 4px;">
            <span th:text="${resetUrl}">Reset URL</span>
        </p>
        
        <div class="security-notice">
            <h3>🛡️ Security Information</h3>
            <ul>
                <li>This link will expire in <span th:text="${expiryTime}">24 hours</span></li>
                <li>If you didn't request this reset, please ignore this email</li>
                <li>Your password will remain unchanged until you create a new one</li>
                <li>For security reasons, we recommend using a strong, unique password</li>
            </ul>
        </div>
        
        <p><strong>Didn't request this?</strong><br>
           If you didn't request a password reset, you can safely ignore this email. Your account remains secure.</p>
        
        <p>If you have any concerns about your account security, please contact our support team immediately.</p>
        
        <p>Best regards,<br>
           The Security Team<br>
           <span th:text="${companyName}">Arihant Capital</span></p>
    </div>
    
    <div class="footer">
        <p>&copy; 2024 <span th:text="${companyName}">Arihant Capital</span>. All rights reserved.</p>
        <p>This is an automated security email. Please do not reply to this email.</p>
    </div>
</body>
</html>
