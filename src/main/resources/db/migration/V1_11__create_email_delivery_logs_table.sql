-- Flyway Migration Script
-- File: src/main/resources/db/migration/V1_11__create_email_delivery_logs_table.sql

-- Create ENUM type for email status
CREATE TYPE email_status AS ENUM ('PENDING', 'SENT', 'FAILED', 'DELIVERED', 'BOUNCED');

CREATE TABLE email_delivery_logs (
    id BIGSERIAL PRIMARY KEY,
    recipients TEXT,
    subject VA<PERSON><PERSON><PERSON>(500),
    template_name VARCHA<PERSON>(100),
    status email_status,
    provider VARCHAR(50),
    attempts INTEGER DEFAULT 0,
    error_message TEXT,
    sent_at TIMESTAMP,
    created_at TIMESTAMP(6) NOT NULL,
    updated_at TIMESTAMP(6) NOT NULL
);

-- Create indexes for better performance
CREATE INDEX idx_email_delivery_logs_status ON email_delivery_logs(status);
CREATE INDEX idx_email_delivery_logs_provider ON email_delivery_logs(provider);
CREATE INDEX idx_email_delivery_logs_template_name ON email_delivery_logs(template_name);
CREATE INDEX idx_email_delivery_logs_sent_at ON email_delivery_logs(sent_at);
CREATE INDEX idx_email_delivery_logs_created_at ON email_delivery_logs(created_at);
CREATE INDEX idx_email_delivery_logs_attempts ON email_delivery_logs(attempts);

-- Create composite index for common queries (status + created_at)
CREATE INDEX idx_email_delivery_logs_status_created_at ON email_delivery_logs(status, created_at);

-- Create partial index for failed emails (for retry processing)
CREATE INDEX idx_email_delivery_logs_failed_attempts ON email_delivery_logs(attempts, created_at) 
WHERE status IN ('FAILED', 'PENDING');

-- Add comments for documentation
COMMENT ON TYPE email_status IS 'Enum type for email delivery status (PENDING, SENT, FAILED, DELIVERED, BOUNCED)';
COMMENT ON TABLE email_delivery_logs IS 'Table storing email delivery logs and status tracking';
COMMENT ON COLUMN email_delivery_logs.id IS 'Primary key - unique identifier for each email delivery log';
COMMENT ON COLUMN email_delivery_logs.recipients IS 'Email recipients (comma-separated for multiple recipients)';
COMMENT ON COLUMN email_delivery_logs.subject IS 'Email subject line';
COMMENT ON COLUMN email_delivery_logs.template_name IS 'Name of the email template used';
COMMENT ON COLUMN email_delivery_logs.status IS 'Current delivery status of the email';
COMMENT ON COLUMN email_delivery_logs.provider IS 'Email service provider used (SMTP/SendGrid/AWS SES/etc)';
COMMENT ON COLUMN email_delivery_logs.attempts IS 'Number of delivery attempts made';
COMMENT ON COLUMN email_delivery_logs.error_message IS 'Error message if delivery failed';
COMMENT ON COLUMN email_delivery_logs.sent_at IS 'Timestamp when email was successfully sent';
COMMENT ON COLUMN email_delivery_logs.created_at IS 'Timestamp when the log entry was created';
COMMENT ON COLUMN email_delivery_logs.updated_at IS 'Timestamp when the log entry was last updated';
