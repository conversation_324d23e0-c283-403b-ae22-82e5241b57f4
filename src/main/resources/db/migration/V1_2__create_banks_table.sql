-- Flyway Migration Script
-- File: src/main/resources/db/migration/V1_2__create_banks_table.sql

CREATE TABLE banks (
    id BIGSERIAL PRIMARY KEY,
    identifier VARCHAR(100) NOT NULL UNIQUE,
    created_at TIMESTAMP(6) NOT NULL,
    updated_at TIMESTAMP(6) NOT NULL
);

-- Create index for better performance on timestamp queries
CREATE INDEX idx_banks_identifier ON banks(identifier);
CREATE INDEX idx_banks_created_at ON banks(created_at);

-- Add comments for documentation
COMMENT ON TABLE banks IS 'Table storing bank information';
COMMENT ON COLUMN banks.id IS 'Primary key - unique identifier for each bank';
COMMENT ON COLUMN banks.created_at IS 'Timestamp when the record was created';
COMMENT ON COLUMN banks.updated_at IS 'Timestamp when the record was last updated';