-- Flyway Migration Script
-- File: src/main/resources/db/migration/V1_3__create_bse_pan_statuses_table.sql

CREATE TABLE bse_pan_statuses (
    id BIGSERIAL PRIMARY KEY,
    identifier VARCHAR(100) NOT NULL UNIQUE,
    client_code VARCHAR(10) NOT NULL UNIQUE,
    client_name VARCHAR(255),
    a<PERSON><PERSON><PERSON>_seed_flag VARCHAR(10),
    created_on TIMESTAMP,
    name_on_pan_card VARCHAR(255),
    pan_name VARCHAR(255),
    pan_number VARCHAR(10),
    pan_validation_on VARCHAR(50),
    pan_status VARCHAR(50),
    created_at TIMESTAMP(6) NOT NULL,
    updated_at TIMESTAMP(6) NOT NULL
);

-- Add foreign key constraint
ALTER TABLE bse_pan_statuses ADD CONSTRAINT fk_bse_pan_statuses_client_code
FOREIGN KEY (client_code) REFERENCES clients(client_code);

-- Create indexes for better performance
CREATE INDEX idx_bse_pan_statuses_identifier ON bse_pan_statuses(identifier);
CREATE INDEX idx_bse_pan_statuses_client_code ON bse_pan_statuses(client_code);
CREATE INDEX idx_bse_pan_statuses_pan_number ON bse_pan_statuses(pan_number);
CREATE INDEX idx_bse_pan_statuses_pan_status ON bse_pan_statuses(pan_status);
CREATE INDEX idx_bse_pan_statuses_created_on ON bse_pan_statuses(created_on);
CREATE INDEX idx_bse_pan_statuses_created_at ON bse_pan_statuses(created_at);

-- Add comments for documentation
COMMENT ON TABLE bse_pan_statuses IS 'Table storing BSE PAN status information for clients';
COMMENT ON COLUMN bse_pan_statuses.id IS 'Primary key - unique identifier for each BSE PAN status record';
COMMENT ON COLUMN bse_pan_statuses.client_code IS 'Unique code identifying the client';
COMMENT ON COLUMN bse_pan_statuses.client_name IS 'Name of the client';
COMMENT ON COLUMN bse_pan_statuses.aadhaar_seed_flag IS 'Flag indicating Aadhaar seeding status';
COMMENT ON COLUMN bse_pan_statuses.created_on IS 'Date and time when the PAN status was created';
COMMENT ON COLUMN bse_pan_statuses.name_on_pan_card IS 'Name as it appears on the PAN card';
COMMENT ON COLUMN bse_pan_statuses.pan_name IS 'PAN holder name';
COMMENT ON COLUMN bse_pan_statuses.pan_number IS 'PAN number';
COMMENT ON COLUMN bse_pan_statuses.pan_validation_on IS 'Date/time when PAN validation was performed';
COMMENT ON COLUMN bse_pan_statuses.pan_status IS 'Current status of the PAN validation';
COMMENT ON COLUMN bse_pan_statuses.created_at IS 'Timestamp when the record was created';
COMMENT ON COLUMN bse_pan_statuses.updated_at IS 'Timestamp when the record was last updated';