-- Flyway Migration Script
-- File: src/main/resources/db/migration/V1_4__create_clients_table.sql

CREATE TABLE clients (
    id BIGSERIAL PRIMARY KEY,
    identifier VARCHAR(100) NOT NULL UNIQUE,
    kyc_mode VARCHAR(10),
    client_code VARCHAR(10) NOT NULL UNIQUE,
    client_name VARCHAR(255),
    pan_number VARCHAR(10),
    name_as_per_pan VARCHAR(255),
    client_type VARCHAR(50),
    client_sub_type VARCHAR(50),
    branch_code VARCHAR(50),
    group_code VARCHAR(50),
    email VARCHAR(255),
    mobile VARCHAR(20),
    dob_or_doi VARCHAR(50),
    gender VARCHAR(20),
    marital_status VARCHAR(50),
    father_or_spouse_name VARCHA<PERSON>(255),
    kra_code VARCHAR(50),
    segments VARCHAR(10)[],
    client_status VARCHAR(50),
    opted_for_upi VARCHAR(10),
    ddpi VARCHAR(10),
    ddpi_date VARCHAR(50),
    poa VARCHAR(10),
    poa_date VARCHAR(50),
    agreement_date VARCHAR(50),
    ckyc_ref_no VARCHAR(100),
    pep VARCHAR(10),
    income_range VARCHAR(50),
    income_date VARCHAR(50),
    address_proof VARCHAR(100),
    address_proof_ref_no VARCHAR(100),
    address1 VARCHAR(255),
    address2 VARCHAR(255),
    address3 VARCHAR(255),
    city VARCHAR(100),
    state VARCHAR(100),
    country VARCHAR(100),
    pincode VARCHAR(20),
    nationality VARCHAR(100),
    residential_status VARCHAR(50),
    occupation VARCHAR(100),
    created_at TIMESTAMP(6) NOT NULL,
    updated_at TIMESTAMP(6) NOT NULL
);

-- Create indexes for better performance
CREATE INDEX idx_clients_identifier ON clients(identifier);
CREATE INDEX idx_clients_client_code ON clients(client_code);
CREATE INDEX idx_clients_pan_number ON clients(pan_number);
CREATE INDEX idx_clients_email ON clients(email);
CREATE INDEX idx_clients_mobile ON clients(mobile);
CREATE INDEX idx_clients_branch_code ON clients(branch_code);
CREATE INDEX idx_clients_group_code ON clients(group_code);
CREATE INDEX idx_clients_client_status ON clients(client_status);
CREATE INDEX idx_clients_client_type ON clients(client_type);
CREATE INDEX idx_clients_kra_code ON clients(kra_code);
CREATE INDEX idx_clients_created_at ON clients(created_at);

-- Add comments for documentation
COMMENT ON TABLE clients IS 'Table storing client information and KYC details';
COMMENT ON COLUMN clients.id IS 'Primary key - unique identifier for each client';
COMMENT ON COLUMN clients.kyc_mode IS 'KYC mode used for client onboarding';
COMMENT ON COLUMN clients.client_code IS 'Unique code identifying the client';
COMMENT ON COLUMN clients.client_name IS 'Full name of the client';
COMMENT ON COLUMN clients.pan_number IS 'PAN number of the client';
COMMENT ON COLUMN clients.name_as_per_pan IS 'Name as it appears on PAN card';
COMMENT ON COLUMN clients.client_type IS 'Type of client (individual/corporate/etc)';
COMMENT ON COLUMN clients.client_sub_type IS 'Sub-type classification of client';
COMMENT ON COLUMN clients.branch_code IS 'Branch code where client is registered';
COMMENT ON COLUMN clients.group_code IS 'Group code for client classification';
COMMENT ON COLUMN clients.email IS 'Email address of the client';
COMMENT ON COLUMN clients.mobile IS 'Mobile number of the client';
COMMENT ON COLUMN clients.dob_or_doi IS 'Date of birth for individual or date of incorporation for corporate';
COMMENT ON COLUMN clients.gender IS 'Gender of the client';
COMMENT ON COLUMN clients.marital_status IS 'Marital status of the client';
COMMENT ON COLUMN clients.father_or_spouse_name IS 'Father or spouse name';
COMMENT ON COLUMN clients.kra_code IS 'KRA (KYC Registration Agency) code';
COMMENT ON COLUMN clients.segments IS 'Trading segments enabled for client (stored as JSON array)';
COMMENT ON COLUMN clients.client_status IS 'Current status of the client account';
COMMENT ON COLUMN clients.opted_for_upi IS 'Whether client opted for UPI payments';
COMMENT ON COLUMN clients.ddpi IS 'DDPI (Demat Debit and Pledge Instruction) status';
COMMENT ON COLUMN clients.ddpi_date IS 'Date of DDPI registration';
COMMENT ON COLUMN clients.poa IS 'Power of Attorney status';
COMMENT ON COLUMN clients.poa_date IS 'Date of POA registration';
COMMENT ON COLUMN clients.agreement_date IS 'Date of client agreement';
COMMENT ON COLUMN clients.ckyc_ref_no IS 'Central KYC reference number';
COMMENT ON COLUMN clients.pep IS 'Politically Exposed Person flag';
COMMENT ON COLUMN clients.income_range IS 'Income range of the client';
COMMENT ON COLUMN clients.income_date IS 'Date of income declaration';
COMMENT ON COLUMN clients.address_proof IS 'Type of address proof provided';
COMMENT ON COLUMN clients.address_proof_ref_no IS 'Reference number of address proof';
COMMENT ON COLUMN clients.address1 IS 'Address line 1';
COMMENT ON COLUMN clients.address2 IS 'Address line 2';
COMMENT ON COLUMN clients.address3 IS 'Address line 3';
COMMENT ON COLUMN clients.city IS 'City';
COMMENT ON COLUMN clients.state IS 'State';
COMMENT ON COLUMN clients.country IS 'Country';
COMMENT ON COLUMN clients.pincode IS 'PIN/ZIP code';
COMMENT ON COLUMN clients.nationality IS 'Nationality of the client';
COMMENT ON COLUMN clients.residential_status IS 'Residential status (resident/non-resident)';
COMMENT ON COLUMN clients.occupation IS 'Occupation of the client';
COMMENT ON COLUMN clients.created_at IS 'Timestamp when the record was created';
COMMENT ON COLUMN clients.updated_at IS 'Timestamp when the record was last updated';