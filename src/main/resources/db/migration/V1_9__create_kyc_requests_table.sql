-- Flyway Migration Script
-- File: src/main/resources/db/migration/V1_9__create_kyc_requests_table.sql

CREATE TABLE kyc_requests (
    id BIGSERIAL PRIMARY KEY,
    identifier VARCHAR(100) NOT NULL UNIQUE,
    client_code VARCHAR(10) NOT NULL,
    kyc_flow VARCHAR(50) NOT NULL,
    activities VARCHAR(255) NOT NULL,
    source VARCHAR(50) NOT NULL,
    depository_flag VARCHAR(1) NOT NULL DEFAULT 'Y',
    validation_status VARCHAR(50) NOT NULL,
    validation_message TEXT NOT NULL,
    created_at TIMESTAMP(6) NOT NULL,
    updated_at TIMESTAMP(6) NOT NULL
);

-- Add foreign key constraint
ALTER TABLE kyc_requests ADD CONSTRAINT fk_kyc_requests_client_code
FOREIGN KEY (client_code) REFERENCES clients(client_code);

-- Create indexes for better performance
CREATE INDEX idx_kyc_requests_identifier ON kyc_requests(identifier);
CREATE INDEX idx_kyc_requests_client_code ON kyc_requests(client_code);
CREATE INDEX idx_kyc_requests_kyc_flow ON kyc_requests(kyc_flow);
CREATE INDEX idx_kyc_requests_source ON kyc_requests(source);
CREATE INDEX idx_kyc_requests_depository_flag ON kyc_requests(depository_flag);
CREATE INDEX idx_kyc_requests_validation_status ON kyc_requests(validation_status);
CREATE INDEX idx_kyc_requests_created_at ON kyc_requests(created_at);
CREATE INDEX idx_kyc_requests_updated_at ON kyc_requests(updated_at);

-- Add comments for documentation
COMMENT ON TABLE kyc_requests IS 'Table storing KYC request information and processing status';
COMMENT ON COLUMN kyc_requests.id IS 'Primary key - unique identifier for each KYC request';
COMMENT ON COLUMN kyc_requests.identifier IS 'Unique identifier for the KYC request';
COMMENT ON COLUMN kyc_requests.client_code IS 'Client code associated with this KYC request';
COMMENT ON COLUMN kyc_requests.kyc_flow IS 'Type of KYC flow being processed (e.g., initial, update, renewal)';
COMMENT ON COLUMN kyc_requests.activities IS 'Activities or steps involved in the KYC process';
COMMENT ON COLUMN kyc_requests.source IS 'Source system or channel that initiated the KYC request';
COMMENT ON COLUMN kyc_requests.depository_flag IS 'Flag indicating whether client has depository account or not (Y/N)';
COMMENT ON COLUMN kyc_requests.validation_status IS 'Current validation status of the KYC request (pending/approved/rejected/etc)';
COMMENT ON COLUMN kyc_requests.validation_message IS 'Detailed message about the validation status or any errors';
COMMENT ON COLUMN kyc_requests.created_at IS 'Timestamp when the KYC request was created';
COMMENT ON COLUMN kyc_requests.updated_at IS 'Timestamp when the KYC request was last updated';
