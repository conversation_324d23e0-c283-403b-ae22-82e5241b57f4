-- Flyway Migration Script
-- File: src/main/resources/db/migration/V1_6__create_client_depositories_table.sql

CREATE TABLE client_depositories (
    id BIGSERIAL PRIMARY KEY,
    identifier VARCHAR(100) NOT NULL UNIQUE,
    client_code VARCHAR(10) NOT NULL,
    depository_name VARCHAR(100),
    depository_id VARCHAR(50),
    depository_type VARCHAR(4),
    depository_client_id VARCHAR(50),
    depository_status VARCHAR(50),
    ddpi VARCHAR(10),
    poa VARCHAR(10),
    created_at TIMESTAMP(6) NOT NULL,
    updated_at TIMESTAMP(6) NOT NULL
);

-- Add foreign key constraint
ALTER TABLE client_depositories ADD CONSTRAINT fk_client_depositories_client_code
FOREIGN KEY (client_code) REFERENCES clients(client_code);

-- Create indexes for better performance
CREATE INDEX idx_client_depositories_identifier ON client_depositories(identifier);
CREATE INDEX idx_client_depositories_client_code ON client_depositories(client_code);
CREATE INDEX idx_client_depositories_depository_id ON client_depositories(depository_id);
CREATE INDEX idx_client_depositories_depository_client_id ON client_depositories(depository_client_id);
CREATE INDEX idx_client_depositories_depository_status ON client_depositories(depository_status);
CREATE INDEX idx_client_depositories_depository_type ON client_depositories(depository_type);
CREATE INDEX idx_client_depositories_ddpi ON client_depositories(ddpi);
CREATE INDEX idx_client_depositories_poa ON client_depositories(poa);
CREATE INDEX idx_client_depositories_created_at ON client_depositories(created_at);

-- Add comments for documentation
COMMENT ON TABLE client_depositories IS 'Table storing client depository account information';
COMMENT ON COLUMN client_depositories.id IS 'Primary key - unique identifier for each client depository record';
COMMENT ON COLUMN client_depositories.identifier IS 'Unique identifier for the client depository record';
COMMENT ON COLUMN client_depositories.client_code IS 'Client code associated with this depository account';
COMMENT ON COLUMN client_depositories.depository_name IS 'Name of the depository (NSDL/CDSL/etc)';
COMMENT ON COLUMN client_depositories.depository_id IS 'Depository identifier/code';
COMMENT ON COLUMN client_depositories.depository_type IS 'Type of depository account';
COMMENT ON COLUMN client_depositories.depository_client_id IS 'Client ID in the depository system';
COMMENT ON COLUMN client_depositories.depository_status IS 'Status of the depository account (active/inactive/etc)';
COMMENT ON COLUMN client_depositories.ddpi IS 'DDPI (Demat Debit and Pledge Instruction) status';
COMMENT ON COLUMN client_depositories.poa IS 'Power of Attorney status';
COMMENT ON COLUMN client_depositories.created_at IS 'Timestamp when the record was created';
COMMENT ON COLUMN client_depositories.updated_at IS 'Timestamp when the record was last updated';