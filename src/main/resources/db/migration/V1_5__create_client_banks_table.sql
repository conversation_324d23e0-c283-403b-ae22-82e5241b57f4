-- Flyway Migration Script
-- File: src/main/resources/db/migration/V1_5__create_client_banks_table.sql

CREATE TABLE client_banks (
    id BIGSERIAL PRIMARY KEY,
    identifier VARCHAR(100) NOT NULL UNIQUE,
    client_code VA<PERSON><PERSON><PERSON>(50),
    bank_name VA<PERSON>HA<PERSON>(255),
    bank_branch VARCHAR(255),
    bank_account_type VARCHAR(50),
    bank_account_number VARCHAR(50),
    bank_ifsc_code VA<PERSON>HAR(20),
    bank_micr_code VARCHAR(20),
    bank_code VARCHAR(50),
    bank_address TEXT,
    default_bank VARCHAR(10),
    created_at TIMESTAMP(6) NOT NULL,
    updated_at TIMESTAMP(6) NOT NULL
);

-- Add foreign key constraint
ALTER TABLE client_banks ADD CONSTRAINT fk_client_banks_client_code
FOREIGN KEY (client_code) REFERENCES clients(client_code);

-- Create indexes for better performance
CREATE INDEX idx_client_banks_identifier ON client_banks(identifier);
CREATE INDEX idx_client_banks_client_code ON client_banks(client_code);
CREATE INDEX idx_client_banks_bank_account_number ON client_banks(bank_account_number);
CREATE INDEX idx_client_banks_bank_ifsc_code ON client_banks(bank_ifsc_code);
CREATE INDEX idx_client_banks_bank_code ON client_banks(bank_code);
CREATE INDEX idx_client_banks_default_bank ON client_banks(default_bank);
CREATE INDEX idx_client_banks_created_at ON client_banks(created_at);


-- Add comments for documentation
COMMENT ON TABLE client_banks IS 'Table storing client bank account information';
COMMENT ON COLUMN client_banks.id IS 'Primary key - unique identifier for each client bank record';
COMMENT ON COLUMN client_banks.identifier IS 'Unique identifier for the client bank record';
COMMENT ON COLUMN client_banks.client_code IS 'Client code associated with this bank account';
COMMENT ON COLUMN client_banks.bank_name IS 'Name of the bank';
COMMENT ON COLUMN client_banks.bank_branch IS 'Branch name of the bank';
COMMENT ON COLUMN client_banks.bank_account_type IS 'Type of bank account (savings/current/etc)';
COMMENT ON COLUMN client_banks.bank_account_number IS 'Bank account number';
COMMENT ON COLUMN client_banks.bank_ifsc_code IS 'IFSC code of the bank branch';
COMMENT ON COLUMN client_banks.bank_micr_code IS 'MICR code of the bank branch';
COMMENT ON COLUMN client_banks.bank_code IS 'Internal bank code';
COMMENT ON COLUMN client_banks.bank_address IS 'Address of the bank branch';
COMMENT ON COLUMN client_banks.default_bank IS 'Flag indicating if this is the default bank for the client';
COMMENT ON COLUMN client_banks.created_at IS 'Timestamp when the record was created';
COMMENT ON COLUMN client_banks.updated_at IS 'Timestamp when the record was last updated';

