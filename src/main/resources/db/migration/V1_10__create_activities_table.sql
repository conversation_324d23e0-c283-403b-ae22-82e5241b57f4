-- Flyway Migration Script
-- File: src/main/resources/db/migration/V1_10__create_activities_table.sql

-- Create ENUM type for event actions
CREATE TYPE event_action AS ENUM ('createBseUcc', 'modifyBseUcc','resetPasswordBseUcc','getPanStatusBseUcc','addCustodianDataBseUcc','createNseUcc','modifyNseUcc','getPanStatusNseUcc','getUccPanStatusNseUcc','createBseStarMutualFund','modifyBseStarMutualFund','addNomineeBseStarMutualFund','modifyNomineeBseStarMutualFund','createMsilClient','modifyMsilClient','createKraData','modifyKraData','uploadKraFiles','getPanStatusKra','verifyCkyc','downloadCkyc','sendEmail','sendSms','sendWhatsapp','send','fetch','other');

CREATE TABLE activities (
    id BIGSERIAL PRIMARY KEY,
    kyc_request_id VARCHAR(100) NOT NULL UNIQUE,
    action event_action NOT NULL,
    event_name VARCHAR(255) NOT NULL,
    status VARCHAR(50) NOT NULL,
    remark TEXT NOT NULL,
    subject_name VARCHAR(255) NOT NULL,
    meta_data JSONB,
    created_at TIMESTAMP(6) NOT NULL,
    updated_at TIMESTAMP(6) NOT NULL
);

-- Add foreign key constraint to kyc_requests table
ALTER TABLE activities ADD CONSTRAINT fk_activities_kyc_request_id
FOREIGN KEY (kyc_request_id) REFERENCES kyc_requests(identifier);

-- Create indexes for better performance
CREATE INDEX idx_activities_kyc_request_id ON activities(kyc_request_id);
CREATE INDEX idx_activities_action ON activities(action);
CREATE INDEX idx_activities_event_name ON activities(event_name);
CREATE INDEX idx_activities_status ON activities(status);
CREATE INDEX idx_activities_subject_name ON activities(subject_name);
CREATE INDEX idx_activities_created_at ON activities(created_at);
CREATE INDEX idx_activities_updated_at ON activities(updated_at);

-- Create GIN index for JSONB meta_data for efficient JSON queries
CREATE INDEX idx_activities_meta_data_gin ON activities USING GIN (meta_data);

-- Add comments for documentation
COMMENT ON TYPE event_action IS 'Enum type for activity event actions (new, modify)';
COMMENT ON TABLE activities IS 'Table storing activity logs and events related to KYC request processing';
COMMENT ON COLUMN activities.id IS 'Primary key - unique identifier for each activity record';
COMMENT ON COLUMN activities.kyc_request_id IS 'Reference to the KYC request identifier this activity belongs to';
COMMENT ON COLUMN activities.action IS 'Type of action performed (new/modify/send/fetch)';
COMMENT ON COLUMN activities.event_name IS 'Name or description of the event that occurred';
COMMENT ON COLUMN activities.status IS 'Current status of the activity (pending/completed/failed/etc)';
COMMENT ON COLUMN activities.remark IS 'Additional remarks or notes about the activity';
COMMENT ON COLUMN activities.subject_name IS 'Name of the subject or entity involved in the activity';
COMMENT ON COLUMN activities.meta_data IS 'Additional metadata stored as JSON for flexible data storage';
COMMENT ON COLUMN activities.created_at IS 'Timestamp when the activity was created';
COMMENT ON COLUMN activities.updated_at IS 'Timestamp when the activity was last updated';
