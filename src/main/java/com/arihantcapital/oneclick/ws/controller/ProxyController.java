package com.arihantcapital.oneclick.ws.controller;

import com.arihantcapital.oneclick.service.ProxyService;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = "/api/proxy")
@RequiredArgsConstructor
public class ProxyController {

    private final ProxyService proxyService;

    @PostMapping(value = {"", "/"})
    public ResponseEntity<JsonNode> nsdlEpiProxy(
            @RequestParam(value = "url", required = true) String url,
            @RequestBody JsonNode payload,
            @RequestHeader HttpHeaders headers)
            throws Exception {
        return ResponseEntity.ok(proxyService.proxyRequest(url, payload, headers));
    }
}
