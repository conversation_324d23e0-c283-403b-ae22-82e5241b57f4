package com.arihantcapital.oneclick.ws.controller;

import com.arihantcapital.oneclick.domain.entity.Client;
import com.arihantcapital.oneclick.service.ClientService;
import com.arihantcapital.oneclick.utils.CSVHelper;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping(value = "/api/clients")
@RequiredArgsConstructor
public class ClientController {

    private final ClientService clientService;

    @GetMapping(value = "/list")
    public ResponseEntity<List<Client>> getClients() {
        return ResponseEntity.ok(clientService.getClients());
    }

    @PostMapping("/sync-csv")
    public ResponseEntity<String> uploadClientsCSV(@RequestParam("file") MultipartFile file) {
        try {
            if (!CSVHelper.hasCSVFormat(file)) {
                return ResponseEntity.badRequest().body("Please upload a CSV file containing client codes!");
            }

            List<Map<String, String>> csvData = CSVHelper.readCSVFromMultipartFile(file);

            System.out.println("CSV Data: " + csvData);

            // Process your data
            csvData.forEach(row -> {
                System.out.println("Row: " + row);
                clientService.syncClient(row.get("clientCode"));
            });

            return ResponseEntity.ok("CSV processed successfully!");
        } catch (IOException e) {
            return ResponseEntity.status(500).body("Error processing CSV: " + e.getMessage());
        }
    }
}
