package com.arihantcapital.oneclick.ws.controller;

import com.arihantcapital.oneclick.service.KycService;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api/kyc")
@RequiredArgsConstructor
public class KycController {

    private final KycService kycService;

    @PostMapping(value = "/request")
    public ResponseEntity<JsonNode> requestKyc() {
        return ResponseEntity.ok(kycService.processKycRequest());
    }
}
