package com.arihantcapital.oneclick.ws.controller;

import com.arihantcapital.oneclick.service.ReKycService;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api/rekyc")
@RequiredArgsConstructor
public class ReKycController {

    private final ReKycService reKycService;

    @PostMapping(value = "/request")
    public ResponseEntity<JsonNode> requestKyc() {
        return ResponseEntity.ok(reKycService.processReKycRequest());
    }
}
