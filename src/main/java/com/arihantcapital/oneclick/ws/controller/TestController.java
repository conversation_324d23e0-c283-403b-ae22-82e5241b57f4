package com.arihantcapital.oneclick.ws.controller;

import com.arihantcapital.oneclick.core.email.EmailManagerService;
import com.arihantcapital.oneclick.core.model.email.EmailAttachment;
import com.arihantcapital.oneclick.core.model.email.EmailDeliveryResult;
import com.arihantcapital.oneclick.utils.CSVHelper;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping(value = "/api/test")
@RequiredArgsConstructor
public class TestController {

    private final EmailManagerService emailManagerService;

    @PostMapping("/upload-csv")
    public ResponseEntity<String> uploadCSV(@RequestParam("file") MultipartFile file) {
        try {
            if (!CSVHelper.hasCSVFormat(file)) {
                return ResponseEntity.badRequest().body("Please upload a CSV file!");
            }

            List<Map<String, String>> csvData = CSVHelper.readCSVFromMultipartFile(file);

            System.out.println("CSV Data: " + csvData);

            // Process your data
            csvData.forEach(row -> {
                System.out.println("Row: " + row);
            });

            return ResponseEntity.ok("CSV processed successfully!");
        } catch (IOException e) {
            return ResponseEntity.status(500).body("Error processing CSV: " + e.getMessage());
        }
    }

    @GetMapping("/sample-onboarding-email")
    public ResponseEntity<?> sendSampleOnboardingEmail(@RequestParam(name = "email", required = true) String email) {
        try {

            // Send email with template
            //            EmailDeliveryResult result = emailManagerService.sendEmailWithTemplate("onboarding", email,
            // "Welcome to Arihant Capital", Map.of());

            // Send email with template and attachments

            // Read Pdf file
            FileInputStream fis = new FileInputStream("resources/kra/one_pager_template.pdf");
            byte[] pdfBytes = fis.readAllBytes();
            EmailAttachment pdfAttachment = EmailManagerService.createPdfAttachment("AOF.pdf", pdfBytes);
            EmailDeliveryResult result2 = emailManagerService.sendEmailWithTemplateAndAttachments(
                    "onboarding",
                    email,
                    "Welcome to Arihant Capital",
                    Map.of(),
                    Collections.singletonList(pdfAttachment));

            return ResponseEntity.ok(result2);

        } catch (Exception e) {
            return ResponseEntity.badRequest().body("Error sending sample onboarding email: " + e.getMessage());
        }
    }
}
