package com.arihantcapital.oneclick.domain.entity.activity;

import java.util.Arrays;
import java.util.Optional;

public enum EventAction {
    CREATE_BSE_UCC("createBseUcc"),
    MODIFY_BSE_UCC("modifyBseUcc"),
    RESET_PASSWORD_BSE_UCC("resetPasswordBseUcc"),
    GET_PAN_STATUS_BSE_UCC("getPanStatusBseUcc"),
    ADD_CUSTODIAN_DATA_BSE_UCC("addCustodianDataBseUcc"),
    CREATE_NSE_UCC("createNseUcc"),
    MODIFY_NSE_UCC("modifyNseUcc"),
    GET_PAN_STATUS_NSE_UCC("getPanStatusNseUcc"),
    GET_UCC_PAN_STATUS_NSE_UCC("getUccPanStatusNseUcc"),
    CREATE_BSE_STAR_MUTUAL_FUND("createBseStarMutualFund"),
    MODIFY_BSE_STAR_MUTUAL_FUND("modifyBseStarMutualFund"),
    ADD_NOMINEE_BSE_STAR_MUTUAL_FUND("addNomineeBseStarMutualFund"),
    MODIFY_NOMINEE_BSE_STAR_MUTUAL_FUND("modifyNomineeBseStarMutualFund"),
    CREATE_MSIL_CLIENT("createMsilClient"),
    MODIFY_MSIL_CLIENT("modifyMsilClient"),
    CREATE_KRA_DATA("createKraData"),
    MODIFY_KRA_DATA("modifyKraData"),
    UPLOAD_KRA_FILES("uploadKraFiles"),
    DOWNLOAD_KRA_FILES("downloadKraFiles"),
    GET_PAN_STATUS_KRA("getPanStatusKra"),
    VERIFY_CKYC("verifyCkyc"),
    DOWNLOAD_CKYC("downloadCkyc"),
    SEND_EMAIL("sendEmail"),
    SEND_SMS("sendSms"),
    SEND_WHATSAPP("sendWhatsapp"),
    SEND("send"),
    FETCH("fetch"),
    OTHER("other");

    private final String value;

    EventAction(String value) {
        this.value = value;
    }

    public static Optional<EventAction> fromString(String string) {
        return Optional.ofNullable(string).flatMap(str -> Arrays.stream(values())
                .filter(it -> it.value.equalsIgnoreCase(str))
                .findAny());
    }

    public String getValue() {
        return value;
    }
}
