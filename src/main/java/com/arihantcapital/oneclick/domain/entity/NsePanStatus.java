package com.arihantcapital.oneclick.domain.entity;

import jakarta.persistence.*;
import java.io.Serializable;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

// @Entity
// @Table(name = "nse_pan_statuses")
@Getter
@Setter
public class NsePanStatus implements Serializable {

    private static final ZoneId INDIA_ZONE = ZoneId.of("Asia/Kolkata");

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    private Long id;

    @Column(name = "identifier", unique = true, nullable = false)
    private String identifier;

    @Column(name = "memberCode")
    private String memberCode;

    @Column(name = "clientCode")
    private String clientCode;

    @Column(name = "clientName")
    private String clientName;

    @Column(name = "clientNameDesc")
    private String clientNameDesc;

    @Column(name = "adhrSeedingStatus")
    private String adhrSeedingStatus;

    @Column(name = "pan")
    private String pan;

    @Column(name = "dobOrDoi")
    private String dobOrDoi;

    @Column(name = "panNameMatchingStatus")
    private String panNameMatchingStatus;

    @Column(name = "panDobMatchingStatus")
    private String panDobMatchingStatus;

    @Column(name = "uccStatus")
    private List<UccStatus> uccStatus;

    @Data
    public static class UccStatus {

        @Column(name = "segment")
        private String segment;

        @Column(name = "creationOn")
        private Date creationOn;

        @Column(name = "panStatus")
        private String panStatus;

        @Column(name = "panStatusDate")
        private Date panStatusDate;

        @Column(name = "exchangeStatus")
        private String exchangeStatus;

        public void setCreationOn(ZonedDateTime zonedDateTime) {
            this.creationOn = Date.from(zonedDateTime.toInstant());
        }

        public ZonedDateTime getCreationOnAsZonedDateTime() {
            return creationOn.toInstant().atZone(INDIA_ZONE);
        }

        public void setPanStatusDate(ZonedDateTime zonedDateTime) {
            this.panStatusDate = Date.from(zonedDateTime.toInstant());
        }

        public ZonedDateTime getPanStatusDateAsZonedDateTime() {
            return creationOn.toInstant().atZone(INDIA_ZONE);
        }
    }

    @Column(name = "created_at", nullable = false)
    private Instant createdAt;

    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;

    @PrePersist
    public void prePersist() {
        createdAt = Instant.now();
        updatedAt = Instant.now();
    }

    @PreUpdate
    public void preUpdate() {
        updatedAt = Instant.now();
    }
}
