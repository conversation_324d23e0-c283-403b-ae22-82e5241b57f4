package com.arihantcapital.oneclick.enums;

public enum ClientSubType {
    INDIVIDUAL("INDIVIDUAL", "Individual"),
    HUF("HUF", "Hindu Undivided Family"),
    AOP("AOP", "Association of Persons"),
    TRUST("TRUST", "Trust"),
    BOI("BOI", "Body of Individuals"),
    PF("PF", "Partnership Firm"),
    BCO("BCO", "Body of Corporates"),
    LLP("LLP", "Limited Liability Partnership"),
    OTHER("OTHER", "Other");

    private final String code;
    private final String description;

    ClientSubType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static ClientSubType fromCode(String code) {
        for (ClientSubType clientSubType : ClientSubType.values()) {
            if (clientSubType.getCode().equals(code)) {
                return clientSubType;
            }
        }
        throw new IllegalArgumentException("Invalid client sub type code: " + code);
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
