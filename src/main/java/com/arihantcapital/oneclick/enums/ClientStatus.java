package com.arihantcapital.oneclick.enums;

public enum ClientStatus {
    ACTIVE("ACTIVE", "Active"),
    INACTIVE("INACTIVE", "Inactive");

    private final String code;
    private final String description;

    ClientStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static ClientStatus fromCode(String code) {
        for (ClientStatus clientStatus : ClientStatus.values()) {
            if (clientStatus.getCode().equals(code)) {
                return clientStatus;
            }
        }
        throw new IllegalArgumentException("Invalid client status code: " + code);
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
