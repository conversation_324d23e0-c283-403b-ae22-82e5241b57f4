package com.arihantcapital.oneclick.enums;

public enum PoliticallyExposedPerson {
    NOT_APPLICABLE("NOT_APPLICABLE", "Not Applicable"),
    EXPOSED("EXPOSED", "Exposed"),
    RELATED("RELATED", "Related"),
    NOT_EXPOSED("NOT_EXPOSED", "Not Exposed");

    private final String code;
    private final String description;

    PoliticallyExposedPerson(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static PoliticallyExposedPerson fromCode(String code) {
        for (PoliticallyExposedPerson politicallyExposedPerson : PoliticallyExposedPerson.values()) {
            if (politicallyExposedPerson.getCode().equals(code)) {
                return politicallyExposedPerson;
            }
        }
        throw new IllegalArgumentException("Invalid politically exposed person code: " + code);
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
