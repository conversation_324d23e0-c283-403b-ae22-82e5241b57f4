package com.arihantcapital.oneclick.enums;

public enum MaritalStatus {
    NOT_APPLICABLE("NOT_APPLICABLE", "Not Applicable"),
    MARRIED("MARRIED", "Married"),
    UNMARRIED("UNMARRIED", "Unmarried"),
    <PERSON><PERSON><PERSON>ED("W<PERSON>OW<PERSON>", "Widowed"),
    D<PERSON><PERSON><PERSON>ED("DIVORCED", "Divorced"),
    <PERSON>THER("OTHER", "Other");

    private final String code;
    private final String description;

    MaritalStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static MaritalStatus fromCode(String code) {
        for (MaritalStatus maritalStatus : MaritalStatus.values()) {
            if (maritalStatus.getCode().equals(code)) {
                return maritalStatus;
            }
        }
        throw new IllegalArgumentException("Invalid marital status code: " + code);
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
