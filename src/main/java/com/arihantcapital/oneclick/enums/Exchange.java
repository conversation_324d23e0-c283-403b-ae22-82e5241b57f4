package com.arihantcapital.oneclick.enums;

public enum Exchange {
    BSE("BSE", "Bombay Stock Exchange"),
    NSE("NSE", "National Stock Exchange"),
    MCX("MCX", "Multi Commodity Exchange"),
    NCDEX("NCDEX", "National Commodity and Derivatives Exchange");

    private final String code;
    private final String description;

    Exchange(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static Exchange fromCode(String code) {
        for (Exchange exchange : Exchange.values()) {
            if (exchange.getCode().equals(code)) {
                return exchange;
            }
        }
        throw new IllegalArgumentException("Invalid exchange code: " + code);
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
