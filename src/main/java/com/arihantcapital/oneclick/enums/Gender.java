package com.arihantcapital.oneclick.enums;

public enum Gender {
    NOT_APPLICABLE("NOT_APPLICABLE", "Not Applicable"),
    MALE("MALE", "Male"),
    FEMALE("FEM<PERSON><PERSON>", "Female"),
    OTHER("OTHER", "Other");

    private final String code;
    private final String description;

    Gender(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static Gender fromCode(String code) {
        for (Gender gender : Gender.values()) {
            if (gender.getCode().equals(code)) {
                return gender;
            }
        }
        throw new IllegalArgumentException("Invalid gender code: " + code);
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
