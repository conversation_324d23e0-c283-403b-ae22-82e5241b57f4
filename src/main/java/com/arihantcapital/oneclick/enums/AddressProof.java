package com.arihantcapital.oneclick.enums;

public enum AddressProof {
    UID("UID", "UID (Aad<PERSON>ar Card)"),
    PASSPORT("PASSPORT", "Passport"),
    DRIVING_LICENSE("DRIVING_LICENSE", "Driving License"),
    VOTER_ID_CARD("VOTER_ID_CARD", "Voter Identity Card"),
    NAREGA_JOB_CARD("NAREGA_JOB_CARD", "NAREGA Job Card"),
    BANK_STATEMENT_PASSBOOK("BANK_STATEMENT_PASSBOOK", "Bank Statement/Passbook"),
    GAS_BILL("GAS_BILL", "Gas Bill"),
    TELEPHONE_BILL("T<PERSON><PERSON>HONE_BILL", "Telephone Bill"),
    ELECTRICITY_BILL("ELECTRICITY_BILL", "Electricity Bill"),
    RATION_CARD("RATION_CARD", "Ration Card"),
    CERTIFICATE_OF_INCORPORATION("CERTIFICATE_OF_INCORPORATION", "Certificate of Incorporation/Formation"),
    REGISTRATION_CERTIFICATE("REGISTRATION_CERTIFICATE", "Registration Certificate"),
    OTHER("OTHER", "Other");

    private final String code;
    private final String description;

    AddressProof(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static AddressProof fromCode(String code) {
        for (AddressProof addressProof : AddressProof.values()) {
            if (addressProof.getCode().equals(code)) {
                return addressProof;
            }
        }
        throw new IllegalArgumentException("Invalid address proof code: " + code);
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
