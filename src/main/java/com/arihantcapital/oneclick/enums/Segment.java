package com.arihantcapital.oneclick.enums;

public enum Segment {
    CAP("CAP", "Cash"),
    FNO("FNO", "Futures And Options"),
    SLB("SLB", "Securities Lending And Borrowing"),
    CUR("CUR", "Currency Derivatives"),
    DEBT("DEBT", "Debt Market"),
    COM("COM", "Commodity"),
    MF("MF", "Mutual Fund");

    private final String code;
    private final String description;

    Segment(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static Segment fromCode(String code) {
        for (Segment segment : Segment.values()) {
            if (segment.getCode().equals(code)) {
                return segment;
            }
        }
        throw new IllegalArgumentException("Invalid segment code: " + code);
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
