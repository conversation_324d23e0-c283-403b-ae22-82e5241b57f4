package com.arihantcapital.oneclick.enums;

public enum ClientType {
    INDIVIDUAL("INDIVIDUAL", "Individual"),
    NON_INDIVIDUAL("NON_INDIVIDUAL", "Non-Individual");

    private final String code;
    private final String description;

    ClientType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static ClientType fromCode(String code) {
        for (ClientType clientType : ClientType.values()) {
            if (clientType.getCode().equals(code)) {
                return clientType;
            }
        }
        throw new IllegalArgumentException("Invalid client type code: " + code);
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
