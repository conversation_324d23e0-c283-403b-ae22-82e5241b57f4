package com.arihantcapital.oneclick.enums;

public enum DataFlowStrategy {
    PULL("PULL", "Pull"),
    PUSH("PUSH", "Push");

    private final String code;
    private final String description;

    DataFlowStrategy(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static DataFlowStrategy fromCode(String code) {
        for (DataFlowStrategy dataFlowStrategy : DataFlowStrategy.values()) {
            if (dataFlowStrategy.getCode().equals(code)) {
                return dataFlowStrategy;
            }
        }
        throw new IllegalArgumentException("Invalid data flow strategy code: " + code);
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
