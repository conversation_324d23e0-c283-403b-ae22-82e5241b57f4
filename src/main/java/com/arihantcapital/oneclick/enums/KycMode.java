package com.arihantcapital.oneclick.enums;

public enum KycMode {
    DIGILOCKER("D<PERSON><PERSON><PERSON><PERSON><PERSON>", "DigiLocker"),
    KRA("KRA", "Kra Agency (CVLKRA,NDML,CAMS,KARVY,DOTEX)"),
    PHYSICAL("PHYSICAL", "Physical");

    private final String code;
    private final String description;

    KycMode(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static KycMode fromCode(String code) {
        for (KycMode kycMode : KycMode.values()) {
            if (kycMode.getCode().equals(code)) {
                return kycMode;
            }
        }
        throw new IllegalArgumentException("Invalid kyc mode code: " + code);
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
