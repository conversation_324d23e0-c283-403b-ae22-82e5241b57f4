package com.arihantcapital.oneclick.core.mapper;

import com.arihantcapital.oneclick.core.dto.korp.KorpBranchDetails;
import com.arihantcapital.oneclick.core.dto.korp.KorpClientDetails;
import com.arihantcapital.oneclick.core.service.KorpService;
import com.arihantcapital.oneclick.domain.entity.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class OneclickKorpMapper {

    @Autowired
    static KorpService korpService;

    private static final ObjectMapper objectMapper = new ObjectMapper();

    private static final ZoneId INDIA_ZONE = ZoneId.of("Asia/Kolkata");

    public static final String CLIENT_DATA_SOURCE = "KORP";

    public static final Map<String, String> ClientTypeMapping =
            Map.ofEntries(Map.entry("I", "INDIVIDUAL"), Map.entry("NI", "NON_INDIVIDUAL"));

    // TODO: This list is not completed and updated according to the korp exchanges mapping
    public static final Map<String, String> ClientSubTypeBseMapping = Map.ofEntries(
            Map.entry("I", "INDIVIDUAL"),
            Map.entry("HUF", "HUF"),
            Map.entry("AOP", "AOP"),
            Map.entry("T", "TRUST"),
            Map.entry("BOI", "BOI"),
            Map.entry("PF", "PF"),
            Map.entry("BCO", "BCO"),
            Map.entry("LLP", "LLP"),
            Map.entry("O", "OTHER"));
    public static final Map<String, String> ClientSubTypeNseMapping = Map.ofEntries(
            Map.entry("I", "INDIVIDUAL"),
            Map.entry("HUF", "HUF"),
            Map.entry("AOP", "AOP"),
            Map.entry("T", "TRUST"),
            Map.entry("BOI", "BOI"),
            Map.entry("PF", "PF"),
            Map.entry("BCO", "BCO"),
            Map.entry("LLP", "LLP"),
            Map.entry("O", "OTHER"));
    public static final Map<String, String> ClientSubTypeMcxMapping = Map.ofEntries(
            Map.entry("I", "INDIVIDUAL"),
            Map.entry("HUF", "HUF"),
            Map.entry("AOP", "AOP"),
            Map.entry("T", "TRUST"),
            Map.entry("BOI", "BOI"),
            Map.entry("PF", "PF"),
            Map.entry("BCO", "BCO"),
            Map.entry("LLP", "LLP"),
            Map.entry("O", "OTHER"));

    public static final Map<String, String> KycModeMapping =
            Map.ofEntries(Map.entry("DIG", "DIGILOCKER"), Map.entry("KRA", "KRA"), Map.entry("NRML", "PHYSICAL"));

    public static final Map<String, String> ClientStatusMapping =
            Map.ofEntries(Map.entry("01", "ACTIVE"), Map.entry("", "INACTIVE"));

    public static final Map<String, String> GenderMapping = Map.ofEntries(
            Map.entry("", "NOT_APPLICABLE"), Map.entry("M", "MALE"), Map.entry("F", "FEMALE"), Map.entry("O", "OTHER"));

    public static final Map<String, String> AddressProofMappingIndividual = Map.ofEntries(
            Map.entry("01", "UID"),
            Map.entry("02", "PASSPORT"),
            Map.entry("03", "DRIVING_LICENSE"),
            Map.entry("04", "VOTER_ID_CARD"),
            Map.entry("05", "NAREGA_JOB_CARD"),
            Map.entry("06", "BANK_STATEMENT_PASSBOOK"),
            Map.entry("07", "GAS_BILL"),
            Map.entry("08", "TELEPHONE_BILL"),
            Map.entry("09", "ELECTRICITY_BILL"),
            Map.entry("10", "RATION_CARD"),
            Map.entry("99", "OTHER"));

    public static final Map<String, String> AddressProofMappingNonIndividual = Map.ofEntries(
            Map.entry("02", "CERTIFICATE_OF_INCORPORATION"),
            Map.entry("03", "REGISTRATION_CERTIFICATE"),
            Map.entry("99", "OTHER"));

    public static final Map<String, String> PepMapping = Map.ofEntries(
            Map.entry("", "NOT_APPLICABLE"),
            Map.entry("01", "EXPOSED"),
            Map.entry("02", "RELATED"),
            Map.entry("03", "NOT_EXPOSED"));

    public static final Map<String, String> SegmentMapping = Map.ofEntries(
            Map.entry("CAP", "CAP"),
            Map.entry("FNO", "FNO"),
            Map.entry("SLB", "SLB"),
            Map.entry("CUR", "CURR"),
            Map.entry("D", "DEBT"),
            Map.entry("COM", "COM"),
            Map.entry("MF", "MF"));

    public static final Map<String, String> IncomeRangeMapping = Map.ofEntries(
            Map.entry("00", "NOT_APPLICABLE"),
            Map.entry("01", "<1L>"),
            Map.entry("02", "1L-5L"),
            Map.entry("03", "5L-10L"),
            Map.entry("04", "10L-25L"),
            Map.entry("05", "25L-1CR"),
            Map.entry("06", ">1CR"));

    public static final Map<String, String> OccupationMapping = Map.ofEntries(
            Map.entry("", "NOT_APPLICABLE"),
            Map.entry("01", "PUBLIC_SECTOR"),
            Map.entry("02", "PRIVATE_SECTOR"),
            Map.entry("03", "GOVERNMENT_SERVICE"),
            Map.entry("04", "PROFESSIONAL"),
            Map.entry("05", "SELF_EMPLOYED"),
            Map.entry("06", "RETIRED"),
            Map.entry("07", "HOUSEWIFE"),
            Map.entry("08", "STUDENT"),
            Map.entry("09", "BUSINESS"),
            Map.entry("10", "AGRICULTURIST"),
            Map.entry("11", "OTHERS"));

    public static final Map<String, String> MaritalStatusMapping = Map.ofEntries(
            Map.entry("", "NOT_APPLICABLE"),
            Map.entry("M", "MARRIED"),
            Map.entry("U", "UNMARRIED"),
            Map.entry("D", "DIVORCED"),
            Map.entry("W", "WIDOWED"),
            Map.entry("O", "OTHER"));

    public static final Map<String, Integer> exchangeOrderMap = Map.of("BSE", 0, "NSE", 1, "MCX", 2, "NCDEX", 3);

    public static String getClientSubTypeFromExchange(KorpClientDetails korpClientDetails) {
        List<KorpClientDetails.ExchangeDetail> exchangeDetails = korpClientDetails.getExchangeDetail().stream()
                .filter(exchangeDetail_ -> "Y".equals(exchangeDetail_.getActiveFlag()))
                .sorted((a, b) -> {
                    int orderA = exchangeOrderMap.getOrDefault(a.getExchangeId(), Integer.MAX_VALUE);
                    int orderB = exchangeOrderMap.getOrDefault(b.getExchangeId(), Integer.MAX_VALUE);
                    return Integer.compare(orderA, orderB);
                })
                .toList();

        if (exchangeDetails.isEmpty()) {
            log.error(
                    "No active exchange detail found for client code: {}",
                    korpClientDetails.getKycDetail().getFirst().getClientCode());
            throw new RuntimeException("No active exchange detail found for client code: "
                    + korpClientDetails.getKycDetail().getFirst().getClientCode());
        }

        KorpClientDetails.ExchangeDetail exchangeDetail = exchangeDetails.getFirst();
        String exchange = exchangeDetail.getExchangeId().toUpperCase().trim();

        return switch (exchange) {
            case "BSE" -> ClientSubTypeBseMapping.get(exchangeDetail.getCategoryCode());
            case "NSE" -> ClientSubTypeNseMapping.get(exchangeDetail.getCategoryCode());
            case "MCX" -> ClientSubTypeMcxMapping.get(exchangeDetail.getCategoryCode());
            default -> {
                log.error("Invalid exchange: {}", exchange);
                throw new IllegalArgumentException("Invalid exchange: " + exchange);
            }
        };
    }

    public static List<String> getClientTradingSegments(KorpClientDetails korpClientDetails) {
        Set<String> clientSegments = new HashSet<>();
        List<KorpClientDetails.SegmentDetail> segmentDetail = korpClientDetails.getSegmentDetail().stream()
                .filter(segmentDetail_ -> "Y".equals(segmentDetail_.getTradingAllow()))
                .toList();

        if (segmentDetail.isEmpty()) {
            log.error(
                    "No trading segments found for client code: {}",
                    korpClientDetails.getKycDetail().getFirst().getClientCode());
            throw new RuntimeException("No trading segments found for client code: "
                    + korpClientDetails.getKycDetail().getFirst().getClientCode());
        }

        segmentDetail.forEach(segmentDetail_ -> {
            clientSegments.add(SegmentMapping.get(segmentDetail_.getSegmentId()));
        });

        return clientSegments.stream().toList();
    }

    public static Client getClient(KorpClientDetails korpClientDetails) {
        Client client = new Client();

        KorpClientDetails.KycDetail kycDetail = korpClientDetails.getKycDetail().getFirst();
        KorpClientDetails.ContactDetail emailContactDetail = korpClientDetails.getContactDetail().stream()
                .filter(contactDetail ->
                        "Y".equals(contactDetail.getPrimaryFlag()) && "E".equals(contactDetail.getContactType()))
                .findFirst()
                .orElse(null);
        KorpClientDetails.ContactDetail mobileContactDetail = korpClientDetails.getContactDetail().stream()
                .filter(contactDetail ->
                        "Y".equals(contactDetail.getPrimaryFlag()) && "M".equals(contactDetail.getContactType()))
                .findFirst()
                .orElse(null);
        KorpClientDetails.AddressDetail addressDetail = korpClientDetails.getAddressDetail().stream()
                .filter(addressDetail_ -> "Y".equals(addressDetail_.getAddressPrimary()))
                .findFirst()
                .orElse(null);
        KorpClientDetails.ClientBackOfficeDetail clientBackOfficeDetail =
                korpClientDetails.getClientBackOfficeDetail().getFirst();
        KorpClientDetails.DepositoryDetail primaryDepository = korpClientDetails.getDepositoryDetail().stream()
                .filter(depositoryDetail -> "Y".equals(depositoryDetail.getPrimaryFlag()))
                .findFirst()
                .orElse(null);
        List<KorpClientDetails.SegmentDetail> segmentDetail = korpClientDetails.getSegmentDetail().stream()
                .filter(segmentDetail_ -> "Y".equals(segmentDetail_.getTradingAllow()))
                .toList();

        String clientType = kycDetail.getClientType();

        // Individual Client Mapping
        if (ClientTypeMapping.get(clientType).equals("INDIVIDUAL")) {
            client.setKycMode(KycModeMapping.get(kycDetail.getKycmode()));
            client.setClientCode(kycDetail.getClientCode());
            client.setClientName(kycDetail.getClientName());
            client.setPanNumber(kycDetail.getPanNo());
            client.setClientType(clientType);
            client.setClientSubType("INDIVIDUAL");
            client.setClientStatus(ClientStatusMapping.get(kycDetail.getClientStatus()));
            client.setBranchCode(kycDetail.getBranchId());
            client.setGroupCode("");
            client.setDobOrDoi(kycDetail.getDob());
            client.setGender(GenderMapping.get(kycDetail.getGender().toUpperCase()));
            client.setMaritalStatus(MaritalStatusMapping.get(kycDetail.getMaritalStatus()));
            client.setFatherOrSpouseName(kycDetail.getFatherOrSpouse());
            client.setNationality(kycDetail.getCitizenship());
            client.setResidentialStatus(kycDetail.getResidentialStatus());
            client.setAgreementDate(kycDetail.getOpenDate());
            client.setCkycRefNo(kycDetail.getCkycrefNo());
            client.setPep(PepMapping.get(kycDetail.getPep()));
            client.setIncomeRange(kycDetail.getAnnualIncome());
            client.setIncomeDate(kycDetail.getAnnualIncomeDate());
            client.setOccupation(kycDetail.getOccupation());

            // Client Trading Segments
            if (!segmentDetail.isEmpty()) {
                client.setSegments(getClientTradingSegments(korpClientDetails));
            }

            // Client Primary Depository
            if (primaryDepository != null) {
                client.setDdpi(primaryDepository.getDdpiflag());
                client.setDdpiDate(primaryDepository.getDdpiactiveDate());
                client.setPoa(primaryDepository.getPoaflag());
                client.setPoaDate(primaryDepository.getPoaactiveDate());
            }

            // Client Back Office Detail
            if (clientBackOfficeDetail != null) {
                client.setOptedForUpi(clientBackOfficeDetail.getUpioptFlag());
            }

            // Email Contact Detail
            if (emailContactDetail != null) {
                client.setEmail(emailContactDetail.getContactEmail());
            }

            // Mobile Contact Detail
            if (mobileContactDetail != null) {
                client.setMobile(mobileContactDetail.getContactNo());
            }

            // Address Detail
            if (addressDetail != null) {
                client.setAddress1(addressDetail.getAddressLine1());
                client.setAddress2(addressDetail.getAddressLine2());
                client.setAddress3(addressDetail.getAddressLine3());
                client.setAddressProof(AddressProofMappingIndividual.get(addressDetail.getAddressProof()));
                client.setAddressProofRefNo(addressDetail.getAddressProofRef());
                client.setCity(addressDetail.getAddressCity());
                client.setState(addressDetail.getAddressState());
                client.setCountry(addressDetail.getAddressCountry());
                client.setPincode(addressDetail.getAddressPinCode());
            }
        }

        // Non-Individual Client Mapping
        if (ClientTypeMapping.get(clientType).equals("NON_INDIVIDUAL")) {
            client.setKycMode(KycModeMapping.get(kycDetail.getKycmode()));
            client.setClientCode(kycDetail.getClientCode());
            client.setClientName(kycDetail.getClientName());
            client.setPanNumber(kycDetail.getPanNo());
            client.setClientType(clientType);
            client.setClientSubType(getClientSubTypeFromExchange(korpClientDetails));
            client.setClientStatus(ClientStatusMapping.get(kycDetail.getClientStatus()));
            client.setBranchCode(kycDetail.getBranchId());
            client.setGroupCode("");
            client.setDobOrDoi(kycDetail.getDob());
            client.setGender("NOT_APPLICABLE");
            client.setMaritalStatus("NOT_APPLICABLE");
            client.setFatherOrSpouseName("NOT_APPLICABLE");
            client.setNationality(kycDetail.getCitizenship());
            client.setResidentialStatus(kycDetail.getResidentialStatus());
            client.setAgreementDate(kycDetail.getOpenDate());
            client.setCkycRefNo(kycDetail.getCkycrefNo());
            client.setPep("NOT_APPLICABLE");
            client.setIncomeRange(kycDetail.getAnnualIncome());
            client.setIncomeDate(kycDetail.getAnnualIncomeDate());
            client.setOccupation("NOT_APPLICABLE");

            // Client Trading Segments
            if (!segmentDetail.isEmpty()) {
                client.setSegments(getClientTradingSegments(korpClientDetails));
            }

            // Client Primary Depository
            if (primaryDepository != null) {
                client.setDdpi(primaryDepository.getDdpiflag());
                client.setDdpiDate(primaryDepository.getDdpiactiveDate());
                client.setPoa(primaryDepository.getPoaflag());
                client.setPoaDate(primaryDepository.getPoaactiveDate());
            }

            // Client Back Office Detail
            if (clientBackOfficeDetail != null) {
                client.setOptedForUpi(clientBackOfficeDetail.getUpioptFlag());
            }

            // Email Contact Detail
            if (emailContactDetail != null) {
                client.setEmail(emailContactDetail.getContactEmail());
            }

            // Mobile Contact Detail
            if (mobileContactDetail != null) {
                client.setMobile(mobileContactDetail.getContactNo());
            }

            // Address Detail
            if (addressDetail != null) {
                client.setAddress1(addressDetail.getAddressLine1());
                client.setAddress2(addressDetail.getAddressLine2());
                client.setAddress3(addressDetail.getAddressLine3());
                client.setAddressProof(AddressProofMappingNonIndividual.get(addressDetail.getAddressProof()));
                client.setAddressProofRefNo(addressDetail.getAddressProofRef());
                client.setCity(addressDetail.getAddressCity());
                client.setState(addressDetail.getAddressState());
                client.setCountry(addressDetail.getAddressCountry());
                client.setPincode(addressDetail.getAddressPinCode());
            }
        }

        //        client.setNominees(List.of(""));
        //        client.setBanks(List.of(""));
        //        client.setDepositories(List.of(""));
        return client;
    }

    public static ZonedDateTime convertStringToIndiaZonedDateTime(String date) {
        LocalDateTime localDateTime = LocalDateTime.parse(date);
        return localDateTime.atZone(INDIA_ZONE);
    }

    public static List<ClientNominee> getClientNomineeList(KorpClientDetails korpClientDetails) {
        List<ClientNominee> clientNomineeList = new ArrayList<>();
        korpClientDetails.getNomineeDetail().forEach(nomineeDetail -> {
            ClientNominee clientNominee = new ClientNominee();
            clientNominee.setClientCode(nomineeDetail.getClientCode());
            clientNominee.setName(nomineeDetail.getName());
            clientNominee.setRelationship(nomineeDetail.getRelation());
            clientNominee.setDob(nomineeDetail.getDob());
            clientNominee.setSharePercentage(nomineeDetail.getSharePercentage());
            clientNominee.setMinorNominee(nomineeDetail.getMinorInd());
            clientNomineeList.add(clientNominee);
        });
        return clientNomineeList;
    }

    public static Branch getBranch(KorpBranchDetails korpBranchDetails) {
        Branch branch = new Branch();
        branch.setBranchCode(korpBranchDetails.getBranchId());
        branch.setBranchName(korpBranchDetails.getBranchName());
        branch.setGroupCode(
                StringUtils.isBlank(korpBranchDetails.getCtclbranch()) ? "HO" : korpBranchDetails.getCtclbranch());
        branch.setRegionCode(korpBranchDetails.getRegion());
        branch.setBranchStatus(korpBranchDetails.getBranchStatus());
        branch.setBranchMode("AUTOMATIC");
        return branch;
    }

    /**
     * Gets the primary bank for the client
     *
     * @param korpClientDetails
     * @return ClientBank
     */
    public static ClientBank getClientPrimaryBank(KorpClientDetails korpClientDetails) {
        ClientBank clientBank = new ClientBank();
        KorpClientDetails.BankDetail primaryBank = korpClientDetails.getBankDetail().stream()
                .filter(bankDetail -> "Y".equals(bankDetail.getPrimaryFlag()))
                .findFirst()
                .orElse(null);

        if (primaryBank == null) {
            throw new RuntimeException("Primary bank not found for client code: "
                    + korpClientDetails.getKycDetail().getFirst().getClientCode());
        }

        String identifier = primaryBank.getClientCode() + "-" + primaryBank.getBankIfsc() + "-"
                + primaryBank.getBankAccountNumber();
        clientBank.setIdentifier(identifier);
        clientBank.setClientCode(primaryBank.getClientCode());
        clientBank.setBankName(primaryBank.getBankName());
        clientBank.setBankBranch("");
        clientBank.setBankAccountType(primaryBank.getBankAccountType());
        clientBank.setBankAccountNumber(primaryBank.getBankAccountNumber());
        clientBank.setBankIfscCode(primaryBank.getBankIfsc());
        clientBank.setBankMicrCode(primaryBank.getBankMicr());
        clientBank.setBankCode(primaryBank.getBankCode());
        clientBank.setBankAddress(primaryBank.getBankAddress1());
        clientBank.setDefaultBank(primaryBank.getPrimaryFlag());
        return clientBank;
    }

    /**
     * Gets all bank for the client
     *
     * @param korpClientDetails
     * @return List<ClientBank>
     */
    public static List<ClientBank> getClientBankList(KorpClientDetails korpClientDetails) {
        List<ClientBank> clientBankList = new ArrayList<>();
        korpClientDetails.getBankDetail().forEach(bankDetail -> {
            ClientBank clientBank = new ClientBank();
            String identifier = bankDetail.getClientCode() + "-" + bankDetail.getBankIfsc() + "-"
                    + bankDetail.getBankAccountNumber();
            clientBank.setIdentifier(identifier);
            clientBank.setClientCode(bankDetail.getClientCode());
            clientBank.setBankName(bankDetail.getBankName());
            clientBank.setBankBranch("");
            clientBank.setBankAccountType(bankDetail.getBankAccountType());
            clientBank.setBankAccountNumber(bankDetail.getBankAccountNumber());
            clientBank.setBankIfscCode(bankDetail.getBankIfsc());
            clientBank.setBankMicrCode(bankDetail.getBankMicr());
            clientBank.setBankCode(bankDetail.getBankCode());
            clientBank.setBankAddress(bankDetail.getBankAddress1());
            clientBank.setDefaultBank(bankDetail.getPrimaryFlag());
            clientBankList.add(clientBank);
        });
        return clientBankList;
    }

    /**
     * Gets the primary depository for the client
     *
     * @param korpClientDetails
     * @return ClientDepository
     */
    public static ClientDepository getClientPrimaryDepository(KorpClientDetails korpClientDetails) {
        ClientDepository clientDepository = new ClientDepository();
        KorpClientDetails.DepositoryDetail primaryDepository = korpClientDetails.getDepositoryDetail().stream()
                .filter(depositoryDetail -> "Y".equals(depositoryDetail.getPrimaryFlag()))
                .findFirst()
                .orElse(null);
        if (primaryDepository == null) {
            throw new RuntimeException("Primary depository not found for client code: "
                    + korpClientDetails.getKycDetail().getFirst().getClientCode());
        }

        String identifier = primaryDepository.getClientCode() + "-" + primaryDepository.getDepositoryId() + "-"
                + primaryDepository.getDepositoryClientId();

        clientDepository.setIdentifier(identifier);
        clientDepository.setClientCode(primaryDepository.getClientCode());
        clientDepository.setDepositoryName(primaryDepository.getDepositoryName());
        clientDepository.setDepositoryId(primaryDepository.getDepositoryId());
        clientDepository.setDepositoryType(primaryDepository.getDepositoryType());
        clientDepository.setDepositoryClientId(primaryDepository.getDepositoryClientId());
        clientDepository.setDepositoryStatus(primaryDepository.getStatus().equals("Active") ? "ACTIVE" : "INACTIVE");
        clientDepository.setDdpi(primaryDepository.getDdpiflag());
        clientDepository.setPoa(primaryDepository.getPoaflag());
        return clientDepository;
    }

    /**
     * Gets all depository for the client
     *
     * @param korpClientDetails
     * @return List<ClientDepository>
     */
    public static List<ClientDepository> getClientDepositoryList(KorpClientDetails korpClientDetails) {
        List<ClientDepository> clientDepositoryList = new ArrayList<>();
        korpClientDetails.getDepositoryDetail().forEach(depositoryDetail -> {
            ClientDepository clientDepository = new ClientDepository();
            String identifier = depositoryDetail.getClientCode() + "-" + depositoryDetail.getDepositoryId() + "-"
                    + depositoryDetail.getDepositoryClientId();

            clientDepository.setIdentifier(identifier);
            clientDepository.setClientCode(depositoryDetail.getClientCode());
            clientDepository.setDepositoryName(depositoryDetail.getDepositoryName());
            clientDepository.setDepositoryId(depositoryDetail.getDepositoryId());
            clientDepository.setDepositoryType(depositoryDetail.getDepositoryType());
            clientDepository.setDepositoryClientId(depositoryDetail.getDepositoryClientId());
            clientDepository.setDepositoryStatus(depositoryDetail.getStatus().equals("Active") ? "ACTIVE" : "INACTIVE");
            clientDepository.setDdpi(depositoryDetail.getDdpiflag());
            clientDepository.setPoa(depositoryDetail.getPoaflag());
            clientDepositoryList.add(clientDepository);
        });
        return clientDepositoryList;
    }

    public static void main(String[] args) {
        String dob = "1975-07-06T00:00:00";
        System.out.println("Hello World!");
    }
}
