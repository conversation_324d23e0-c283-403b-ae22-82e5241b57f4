package com.arihantcapital.oneclick.core.mapper;

import com.arihantcapital.oneclick.domain.entity.Client;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class OneclickMsilMapper {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static JsonNode getMsilClientInsertRequest(Client client) {

        String dob = client.getDobOrDoi();
        // Reverse the date format from YYYY-MM-DD to DDMMYYYY
        String parsedDob = dob.replace("-", "");
        String reversedDob = parsedDob.substring(6) + parsedDob.substring(4, 6) + parsedDob.substring(0, 4);

        Map<String, Object> request = new HashMap<>();
        request.put("clientCode", client.getClientCode());
        request.put("clientName", client.getClientName());
        request.put("email", client.getEmail());
        request.put("panNumber", client.getPanNumber());
        request.put("dob", reversedDob);
        request.put("mobile", client.getMobile());

        // Convert Map to JsonNode using ObjectMapper.valueToTree()
        return objectMapper.valueToTree(request);
    }

    public static JsonNode getMsilClientInsertRequest(List<Client> clients) {
        List<JsonNode> request = new ArrayList<>();
        clients.forEach(client -> {
            request.add(getMsilClientInsertRequest(client));
        });
        return objectMapper.valueToTree(request);
    }

    public static JsonNode getMsilClientUpdateRequest(Client client, List<String> fields) {

        Map<String, Object> request = new HashMap<>();
        request.put("clientCode", client.getClientCode());
        fields.forEach(field -> {
            switch (field) {
                case "email" -> request.put("email", client.getEmail());
                case "mobile" -> request.put("mobile", client.getMobile());
            }
        });

        // Convert Map to JsonNode using ObjectMapper.valueToTree()
        return objectMapper.valueToTree(request);
    }

    // Need a helper function to validate the data
    public static boolean validateMsilClientRequest(JsonNode request) {
        // Validate the request
        // 1. Check if the request is not null
        // 2. Check if the request has all the required fields
        // 3. Check if the request has all the required fields with valid data
        return true;
    }


    public static void main(String[] args) {
        String dob = "1975-07-06";
        String parsedDob = dob.substring(8) + dob.substring(5, 7) + dob.substring(0, 4);
        System.out.println(parsedDob);
    }
}
