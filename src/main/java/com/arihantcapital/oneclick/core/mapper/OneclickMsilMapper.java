package com.arihantcapital.oneclick.core.mapper;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class OneclickMsilMapper {

    public static JsonNode getMsilClientInsertRequest() {

        Map<String, Object> request = new HashMap<>();
        request.put("clientCode", "");
        request.put("clientName","");
        request.put("email","");
        request.put("panNumber","");
        request.put("dob","");
        request.put("mobile","");

        return ;
    }

    public static JsonNode getMsilClientUpdateRequest() {

        return null;
    }





}
