package com.arihantcapital.oneclick.core.email;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class EmailHealthCheckService {
    private final List<EmailProvider> emailProviders;

    public EmailHealthCheckService(List<EmailProvider> emailProviders) {
        this.emailProviders = emailProviders;
    }

    @Scheduled(fixedRate = 300000) // Every 5 minutes
    public void checkProviderHealth() {
        emailProviders.parallelStream().forEach(provider -> {
            try {
                provider.testConnection();
                log.info("✅ Provider {} is healthy.", provider.getName());
            } catch (Exception e) {
                log.error("❌ Provider {} is unhealthy: {}", provider.getName(), e.getMessage());
            }
        });
    }

    public Map<String, Boolean> getHealthStatus() {
        return emailProviders.stream().collect(Collectors.toMap(EmailProvider::getName, EmailProvider::isHealthy));
    }
}
