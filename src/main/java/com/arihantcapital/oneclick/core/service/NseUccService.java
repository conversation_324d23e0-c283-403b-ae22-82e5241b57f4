package com.arihantcapital.oneclick.core.service;

import com.arihantcapital.oneclick.OneclickProperties;
import com.arihantcapital.oneclick.core.client.nse.ucc.NseUccClientImpl;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class NseUccService {

    private final OneclickProperties.NseUccConfig config;
    private NseUccClientImpl nseUccClient;

    public NseUccService(OneclickProperties.NseUccConfig config) {
        this.config = config;
        NseUccClientImpl.ClientConfig clientConfig = new NseUccClientImpl.ClientConfig(config);
        this.nseUccClient = new NseUccClientImpl(clientConfig);
        log.info("NSE UCC Service initialized with client config: {}", clientConfig);
    }

    public String authenticate() {
        try {
            return nseUccClient.authenticate();
        } catch (Exception e) {
            log.error(e.getMessage());
            return null;
        }
    }

    public String clientUpload(JsonNode payload) {
        try {
            String token = "";
            return nseUccClient.clientUpload(token, payload);
        } catch (Exception e) {
            log.error(e.getMessage());
            return null;
        }
    }

    public String getPanStatus(JsonNode payload) {

        try {
            String token = "";
            return nseUccClient.getPanStatus(token, payload);
        } catch (Exception e) {
            log.error(e.getMessage());
            return null;
        }
    }

    public String getUccPanStatus(JsonNode payload) {

        try {
            String token = "";
            return nseUccClient.getUccPanStatus(token, payload);
        } catch (Exception e) {
            log.error(e.getMessage());
            return null;
        }
    }
}
