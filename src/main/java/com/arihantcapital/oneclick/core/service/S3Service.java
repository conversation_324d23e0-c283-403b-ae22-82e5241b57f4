package com.arihantcapital.oneclick.core.service;

import com.arihantcapital.oneclick.OneclickProperties;
import jakarta.annotation.PostConstruct;
import java.io.IOException;
import java.io.InputStream;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;
import software.amazon.awssdk.services.s3.presigner.model.PutObjectPresignRequest;

@Slf4j
public class S3Service {

    @Autowired
    private S3Client s3Client;

    @Autowired
    private S3Presigner s3Presigner;

    private final OneclickProperties.S3Config config;
    private final String bucketName;
    private final String region;
    private final boolean healthCheckEnabled;

    public S3Service(OneclickProperties.S3Config config) {
        this.config = config;
        this.bucketName = config.bucketName();
        this.region = config.region();
        this.healthCheckEnabled = config.healthCheck();
        log.info("S3 Config: {}", config);
        log.info("S3 Service initialized");
    }

    @PostConstruct
    public void initializeS3Service() {
        if (healthCheckEnabled) {
            checkS3Connection();
        }
    }

    /**
     * Check S3 connection and bucket accessibility
     */
    public void checkS3Connection() {
        try {
            log.info("Checking S3 connection for bucket: {}", bucketName);

            // Check if bucket exists and is accessible
            HeadBucketRequest headBucketRequest =
                    HeadBucketRequest.builder().bucket(bucketName).build();

            s3Client.headBucket(headBucketRequest);
            log.info("✅ S3 connection successful! Bucket '{}' is accessible.", bucketName);

        } catch (NoSuchBucketException e) {
            log.error("❌ S3 bucket '{}' does not exist or is not accessible: {}", bucketName, e.getMessage());
            if (healthCheckEnabled) {
                throw new RuntimeException("S3 bucket '" + bucketName + "' does not exist or is not accessible", e);
            }

        } catch (Exception e) {
            log.error("❌ Failed to connect to S3: {}", e.getMessage());
            if (healthCheckEnabled) {
                throw new RuntimeException("Failed to connect to S3: " + e.getMessage(), e);
            }
        }
    }

    /**
     * Upload a file to S3
     */
    public String uploadFile(MultipartFile file) throws IOException {
        String key = generateUniqueKey(file.getOriginalFilename());
        return uploadFile(file.getInputStream(), key, file.getContentType());
    }

    /**
     * Upload a file to S3 with custom key
     */
    public String uploadFile(MultipartFile file, String key) throws IOException {
        return uploadFile(file.getInputStream(), key, file.getContentType());
    }

    /**
     * Upload file from InputStream
     */
    public String uploadFile(InputStream inputStream, String key, String contentType) {
        try {
            PutObjectRequest putRequest = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .contentType(contentType)
                    .build();

            s3Client.putObject(putRequest, RequestBody.fromInputStream(inputStream, inputStream.available()));
            return getFileUrl(key);
        } catch (Exception e) {
            throw new RuntimeException("Failed to upload file to S3: " + e.getMessage(), e);
        }
    }

    /**
     * Upload file with metadata
     */
    public String uploadFileWithMetadata(MultipartFile file, Map<String, String> metadata)
            throws IOException, IOException {
        String key = generateUniqueKey(file.getOriginalFilename());

        PutObjectRequest putRequest = PutObjectRequest.builder()
                .bucket(bucketName)
                .key(key)
                .contentType(file.getContentType())
                .metadata(metadata)
                .build();

        s3Client.putObject(putRequest, RequestBody.fromInputStream(file.getInputStream(), file.getSize()));
        return getFileUrl(key);
    }

    /**
     * Download file from S3
     */
    public ResponseInputStream<GetObjectResponse> downloadFile(String key) {
        GetObjectRequest getRequest =
                GetObjectRequest.builder().bucket(bucketName).key(key).build();

        return s3Client.getObject(getRequest);
    }

    /**
     * Delete file from S3
     */
    public void deleteFile(String key) {
        DeleteObjectRequest deleteRequest =
                DeleteObjectRequest.builder().bucket(bucketName).key(key).build();

        s3Client.deleteObject(deleteRequest);
    }

    /**
     * Delete multiple files from S3
     */
    public void deleteFiles(List<String> keys) {
        if (keys.isEmpty()) return;

        List<ObjectIdentifier> objectsToDelete = keys.stream()
                .map(key -> ObjectIdentifier.builder().key(key).build())
                .collect(Collectors.toList());

        Delete delete = Delete.builder().objects(objectsToDelete).build();

        DeleteObjectsRequest deleteRequest =
                DeleteObjectsRequest.builder().bucket(bucketName).delete(delete).build();

        s3Client.deleteObjects(deleteRequest);
    }

    /**
     * Check if file exists in S3
     */
    public boolean fileExists(String key) {
        try {
            HeadObjectRequest headRequest =
                    HeadObjectRequest.builder().bucket(bucketName).key(key).build();

            s3Client.headObject(headRequest);
            return true;
        } catch (NoSuchKeyException e) {
            return false;
        }
    }

    /**
     * Get file metadata
     */
    public Map<String, String> getFileMetadata(String key) {
        HeadObjectRequest headRequest =
                HeadObjectRequest.builder().bucket(bucketName).key(key).build();

        HeadObjectResponse response = s3Client.headObject(headRequest);
        return response.metadata();
    }

    /**
     * List files in bucket with prefix
     */
    public List<String> listFiles(String prefix) {
        ListObjectsV2Request listRequest =
                ListObjectsV2Request.builder().bucket(bucketName).prefix(prefix).build();

        ListObjectsV2Response response = s3Client.listObjectsV2(listRequest);
        return response.contents().stream().map(S3Object::key).collect(Collectors.toList());
    }

    /**
     * List all files in bucket
     */
    public List<String> listAllFiles() {
        return listFiles("");
    }

    /**
     * Copy file within S3
     */
    public void copyFile(String sourceKey, String destinationKey) {
        CopyObjectRequest copyRequest = CopyObjectRequest.builder()
                .sourceBucket(bucketName)
                .sourceKey(sourceKey)
                .destinationBucket(bucketName)
                .destinationKey(destinationKey)
                .build();

        s3Client.copyObject(copyRequest);
    }

    /**
     * Move file within S3 (copy then delete)
     */
    public void moveFile(String sourceKey, String destinationKey) {
        copyFile(sourceKey, destinationKey);
        deleteFile(sourceKey);
    }

    /**
     * Generate presigned URL for file download (valid for 1 hour)
     */
    public String generatePresignedDownloadUrl(String key) {
        return generatePresignedDownloadUrl(key, Duration.ofHours(1));
    }

    /**
     * Generate presigned URL for file download with custom expiration
     */
    public String generatePresignedDownloadUrl(String key, Duration expiration) {
        GetObjectRequest getRequest =
                GetObjectRequest.builder().bucket(bucketName).key(key).build();

        GetObjectPresignRequest presignRequest = GetObjectPresignRequest.builder()
                .signatureDuration(expiration)
                .getObjectRequest(getRequest)
                .build();

        return s3Presigner.presignGetObject(presignRequest).url().toString();
    }

    /**
     * Generate presigned URL for file upload (valid for 1 hour)
     */
    public String generatePresignedUploadUrl(String key) {
        return generatePresignedUploadUrl(key, Duration.ofHours(1));
    }

    /**
     * Generate presigned URL for file upload with custom expiration
     */
    public String generatePresignedUploadUrl(String key, Duration expiration) {
        PutObjectRequest putRequest =
                PutObjectRequest.builder().bucket(bucketName).key(key).build();

        PutObjectPresignRequest presignRequest = PutObjectPresignRequest.builder()
                .signatureDuration(expiration)
                .putObjectRequest(putRequest)
                .build();

        return s3Presigner.presignPutObject(presignRequest).url().toString();
    }

    /**
     * Get public URL for file (if bucket allows public access)
     */
    public String getFileUrl(String key) {
        return String.format("https://%s.s3.%s.amazonaws.com/%s", bucketName, region, key);
    }

    /**
     * Get file size
     */
    public long getFileSize(String key) {
        HeadObjectRequest headRequest =
                HeadObjectRequest.builder().bucket(bucketName).key(key).build();

        HeadObjectResponse response = s3Client.headObject(headRequest);
        return response.contentLength();
    }

    /**
     * Create a folder (prefix) in S3
     */
    public void createFolder(String folderName) {
        String key = folderName.endsWith("/") ? folderName : folderName + "/";

        PutObjectRequest putRequest =
                PutObjectRequest.builder().bucket(bucketName).key(key).build();

        s3Client.putObject(putRequest, RequestBody.empty());
    }

    /**
     * Generate unique key for file upload
     */
    private String generateUniqueKey(String originalFilename) {
        String extension = "";
        if (originalFilename != null && originalFilename.contains(".")) {
            extension = originalFilename.substring(originalFilename.lastIndexOf("."));
        }
        return UUID.randomUUID().toString() + extension;
    }

    /**
     * Generate key with timestamp
     */
    public String generateTimestampKey(String originalFilename) {
        String extension = "";
        if (originalFilename != null && originalFilename.contains(".")) {
            extension = originalFilename.substring(originalFilename.lastIndexOf("."));
        }
        return System.currentTimeMillis() + "_" + originalFilename.replaceAll("[^a-zA-Z0-9.]", "_");
    }

    /**
     * Validate file type
     */
    public boolean isValidFileType(MultipartFile file, List<String> allowedTypes) {
        String contentType = file.getContentType();
        return contentType != null && allowedTypes.contains(contentType.toLowerCase());
    }

    /**
     * Validate file size
     */
    public boolean isValidFileSize(MultipartFile file, long maxSizeInBytes) {
        return file.getSize() <= maxSizeInBytes;
    }
}
