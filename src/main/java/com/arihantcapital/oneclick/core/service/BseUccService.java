package com.arihantcapital.oneclick.core.service;

import com.arihantcapital.oneclick.OneclickProperties;
import com.arihantcapital.oneclick.core.client.bse.ucc.BseUccClientImpl;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class BseUccService {

    private final OneclickProperties.BseUccConfig config;
    private BseUccClientImpl bseUccClient;

    public BseUccService(OneclickProperties.BseUccConfig config) {
        this.config = config;
        BseUccClientImpl.ClientConfig clientConfig = new BseUccClientImpl.ClientConfig(config);
        this.bseUccClient = new BseUccClientImpl(clientConfig);
        log.info("BSE UCC Service initialized with client config: {}", clientConfig);
    }

    public String addUccData() {
        return "";
    }

    public String resetPassword() {
        return "";
    }

    public String getPanStatus(JsonNode payload) {
        try {
            return bseUccClient.getPanStatus(payload);
        } catch (Exception e) {
            log.error(e.getMessage());
            return null;
        }
    }

    public String addCustodianData() {
        return "";
    }
}
