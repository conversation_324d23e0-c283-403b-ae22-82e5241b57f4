package com.arihantcapital.oneclick.core.service;

import com.arihantcapital.oneclick.OneclickProperties;
import com.arihantcapital.oneclick.core.client.kra.cvl.sftp.CvlSftpClientImpl;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CvlSftpService {

    private final OneclickProperties.KraConfig.CvlConfig.SftpConfig config;
    private CvlSftpClientImpl cvlSftpClient;

    public CvlSftpService(OneclickProperties.KraConfig.CvlConfig.SftpConfig config) {
        this.config = config;
        CvlSftpClientImpl.ClientConfig clientConfig = new CvlSftpClientImpl.ClientConfig(config);
        this.cvlSftpClient = new CvlSftpClientImpl(clientConfig);
        log.info("CVLKRA SFTP Service initialized with client config: {}", clientConfig);
    }

    public void connect() {
        try {
            cvlSftpClient.connect();
        } catch (Exception e) {
            log.error("Error connecting to CVL KRA SFTP Server: {}", e.getMessage());
        }
    }
}
