package com.arihantcapital.oneclick.core.service;

import com.itextpdf.forms.PdfAcroForm;
import com.itextpdf.forms.fields.*;
import com.itextpdf.kernel.colors.ColorConstants;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.*;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.layout.properties.UnitValue;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

@Slf4j
@Service(value = "pdfService")
public class PDFService {

    private static final Logger logger = LoggerFactory.getLogger(PDFService.class);

    @Value("${app.pdf.output.directory:${java.io.tmpdir}/pdfs}")
    private String outputDirectory;

    @Value("${app.pdf.default.font:Helvetica}")
    private String defaultFontName;

    // PDF Form Filling Methods

    /**
     * Fill PDF form with provided data and return as bytes
     *
     * @param templatePdfBytes Original PDF template with form fields
     * @param formData         Map containing field names and their values
     * @param flattenForm      Whether to flatten the form (make it non-editable)
     * @return Filled PDF as byte array
     */
    public byte[] fillPdfForm(byte[] templatePdfBytes, Map<String, String> formData, boolean flattenForm)
            throws Exception {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();

        try (ByteArrayInputStream bais = new ByteArrayInputStream(templatePdfBytes);
                PdfReader reader = new PdfReader(bais);
                PdfWriter writer = new PdfWriter(baos);
                PdfDocument pdf = new PdfDocument(reader, writer)) {

            PdfAcroForm form = PdfAcroForm.getAcroForm(pdf, true);

            if (form == null) {
                throw new Exception("PDF does not contain fillable form fields");
            }

            // Fill form fields
            for (Map.Entry<String, String> entry : formData.entrySet()) {
                String fieldName = entry.getKey();
                String fieldValue = entry.getValue();

                PdfFormField field = form.getField(fieldName);
                if (field != null && fieldValue != null) {
                    field.setValue(fieldValue);
                }
            }

            // Flatten form if requested
            if (flattenForm) {
                form.flattenFields();
            }

        } catch (Exception e) {
            logger.error("Error filling PDF form", e);
            throw new Exception("Failed to fill PDF form: " + e.getMessage());
        }

        return baos.toByteArray();
    }

    /**
     * Fill PDF form from file path
     *
     * @param templateFilePath Path to template PDF file
     * @param formData         Map containing field names and their values
     * @param flattenForm      Whether to flatten the form
     * @return Filled PDF as byte array
     */
    public byte[] fillPdfFormFromFile(String templateFilePath, Map<String, String> formData, boolean flattenForm)
            throws Exception {
        byte[] templateBytes = readPdfFile(templateFilePath);
        return fillPdfForm(templateBytes, formData, flattenForm);
    }

    /**
     * Fill PDF form and save to file
     *
     * @param templatePdfBytes Original PDF template
     * @param formData         Form field data
     * @param outputFilename   Output filename
     * @param flattenForm      Whether to flatten the form
     * @return Path to saved file
     */
    public String fillAndSavePdfForm(
            byte[] templatePdfBytes, Map<String, String> formData, String outputFilename, boolean flattenForm)
            throws Exception {
        byte[] filledPdfBytes = fillPdfForm(templatePdfBytes, formData, flattenForm);
        return savePdfToFile(filledPdfBytes, outputFilename);
    }

    /**
     * Get all form field names from a PDF
     *
     * @param pdfBytes PDF file as byte array
     * @return Set of field names
     */
    public Set<String> getPdfFormFieldNames(byte[] pdfBytes) throws Exception {
        try (ByteArrayInputStream bais = new ByteArrayInputStream(pdfBytes);
                PdfReader reader = new PdfReader(bais);
                PdfDocument pdf = new PdfDocument(reader)) {

            PdfAcroForm form = PdfAcroForm.getAcroForm(pdf, false);

            if (form == null) {
                return Set.of();
            }

            return form.getAllFormFields().keySet();

        } catch (Exception e) {
            logger.error("Error getting PDF form field names", e);
            throw new Exception("Failed to get form field names: " + e.getMessage());
        }
    }

    /**
     * Get detailed information about form fields
     *
     * @param pdfBytes PDF file as byte array
     * @return Map containing field information
     */
    public Map<String, Map<String, Object>> getPdfFormFieldInfo(byte[] pdfBytes) throws Exception {
        Map<String, Map<String, Object>> fieldInfo = new HashMap<>();

        try (ByteArrayInputStream bais = new ByteArrayInputStream(pdfBytes);
                PdfReader reader = new PdfReader(bais);
                PdfDocument pdf = new PdfDocument(reader)) {

            PdfAcroForm form = PdfAcroForm.getAcroForm(pdf, false);

            if (form == null) {
                return fieldInfo;
            }

            Map<String, PdfFormField> formFields = form.getAllFormFields();

            for (Map.Entry<String, PdfFormField> entry : formFields.entrySet()) {
                String fieldName = entry.getKey();
                PdfFormField field = entry.getValue();

                Map<String, Object> info = new HashMap<>();
                info.put("fieldName", fieldName);
                info.put("fieldType", getFieldType(field));
                info.put("isRequired", field.isRequired());
                info.put("isReadOnly", field.isReadOnly());
                info.put("currentValue", field.getValueAsString());

                // Add additional info based on field type
                if (field instanceof PdfTextFormField) {
                    PdfTextFormField textField = (PdfTextFormField) field;
                    info.put("maxLength", textField.getMaxLen());
                    info.put("isMultiline", textField.isMultiline());
                    info.put("isPassword", textField.isPassword());
                } else if (field instanceof PdfChoiceFormField) {
                    PdfChoiceFormField choiceField = (PdfChoiceFormField) field;
                    info.put("options", getChoiceOptions(choiceField));
                    info.put("isCombo", choiceField.isCombo());
                }

                fieldInfo.put(fieldName, info);
            }

        } catch (Exception e) {
            logger.error("Error getting PDF form field info", e);
            throw new Exception("Failed to get form field info: " + e.getMessage());
        }

        return fieldInfo;
    }

    /**
     * Fill PDF form with validation
     *
     * @param templatePdfBytes Original PDF template
     * @param formData         Form data to fill
     * @param validateRequired Whether to validate required fields
     * @param flattenForm      Whether to flatten the form
     * @return Filled PDF as byte array
     */
    public byte[] fillPdfFormWithValidation(
            byte[] templatePdfBytes, Map<String, String> formData, boolean validateRequired, boolean flattenForm)
            throws Exception {

        // Get field information for validation
        Map<String, Map<String, Object>> fieldInfo = getPdfFormFieldInfo(templatePdfBytes);

        // Validate required fields if requested
        if (validateRequired) {
            validateRequiredFields(fieldInfo, formData);
        }

        // Validate field types and constraints
        validateFieldData(fieldInfo, formData);

        return fillPdfForm(templatePdfBytes, formData, flattenForm);
    }

    /**
     * Fill multiple PDF forms with the same data
     *
     * @param templatePdfBytes Template PDF
     * @param formDataList     List of form data maps
     * @param flattenForm      Whether to flatten forms
     * @return List of filled PDFs as byte arrays
     */
    public List<byte[]> fillMultiplePdfForms(
            byte[] templatePdfBytes, List<Map<String, String>> formDataList, boolean flattenForm) throws Exception {
        List<byte[]> filledPdfs = new ArrayList<>();

        for (Map<String, String> formData : formDataList) {
            byte[] filledPdf = fillPdfForm(templatePdfBytes, formData, flattenForm);
            filledPdfs.add(filledPdf);
        }

        return filledPdfs;
    }

    /**
     * Create a pre-filled PDF form template for testing
     *
     * @param filename Output filename
     * @return Path to created template
     */
    public String createSamplePdfForm(String filename) throws Exception {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();

        try (PdfWriter writer = new PdfWriter(baos);
                PdfDocument pdf = new PdfDocument(writer);
                Document document = new Document(pdf)) {

            pdf.setDefaultPageSize(PageSize.A4);

            // Add title
            addTitle(document, "Sample Form Template");

            // Create form
            PdfAcroForm form = PdfAcroForm.getAcroForm(pdf, true);

            // Add some sample form fields programmatically
            // Note: In practice, you'd typically use a PDF with pre-created form fields
            document.add(new Paragraph("Name: ").setMarginBottom(5));
            document.add(new Paragraph("Email: ").setMarginBottom(5));
            document.add(new Paragraph("Phone: ").setMarginBottom(5));
            document.add(new Paragraph("Comments: ").setMarginBottom(5));

        } catch (Exception e) {
            logger.error("Error creating sample PDF form", e);
            throw new Exception("Failed to create sample form: " + e.getMessage());
        }

        return savePdfToFile(baos.toByteArray(), filename);
    }

    /**
     * Generate PDF from text content
     */
    public byte[] generatePdfFromText(String content, String title) throws Exception {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();

        try (PdfWriter writer = new PdfWriter(baos);
                PdfDocument pdf = new PdfDocument(writer);
                Document document = new Document(pdf)) {

            // Set page size
            pdf.setDefaultPageSize(PageSize.A4);

            // Add title
            if (title != null && !title.isEmpty()) {
                PdfFont titleFont = PdfFontFactory.createFont();
                Paragraph titleParagraph = new Paragraph(title)
                        .setFont(titleFont)
                        .setFontSize(18)
                        .setTextAlignment(TextAlignment.CENTER)
                        .setMarginBottom(20);
                document.add(titleParagraph);
            }

            // Add content
            PdfFont contentFont = PdfFontFactory.createFont();
            Paragraph contentParagraph =
                    new Paragraph(content).setFont(contentFont).setFontSize(12);
            document.add(contentParagraph);

        } catch (Exception e) {
            logger.error("Error generating PDF from text", e);
            throw new Exception("Failed to generate PDF: " + e.getMessage());
        }

        return baos.toByteArray();
    }

    /**
     * Generate PDF with table data
     */
    public byte[] generatePdfWithTable(List<String> headers, List<List<String>> data, String title) throws Exception {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();

        try (PdfWriter writer = new PdfWriter(baos);
                PdfDocument pdf = new PdfDocument(writer);
                Document document = new Document(pdf)) {

            pdf.setDefaultPageSize(PageSize.A4);

            // Add title
            if (title != null && !title.isEmpty()) {
                addTitle(document, title);
            }

            // Create table
            Table table = new Table(UnitValue.createPercentArray(headers.size()));
            table.setWidth(UnitValue.createPercentValue(100));

            // Add headers
            PdfFont headerFont = PdfFontFactory.createFont();
            for (String header : headers) {
                Cell headerCell = new Cell()
                        .add(new Paragraph(header))
                        .setFont(headerFont)
                        .setBackgroundColor(ColorConstants.LIGHT_GRAY)
                        .setTextAlignment(TextAlignment.CENTER);
                table.addHeaderCell(headerCell);
            }

            // Add data rows
            PdfFont dataFont = PdfFontFactory.createFont();
            for (List<String> row : data) {
                for (String cellData : row) {
                    Cell dataCell = new Cell()
                            .add(new Paragraph(cellData != null ? cellData : ""))
                            .setFont(dataFont)
                            .setFontSize(10);
                    table.addCell(dataCell);
                }
            }

            document.add(table);

        } catch (Exception e) {
            logger.error("Error generating PDF with table", e);
            throw new Exception("Failed to generate PDF with table: " + e.getMessage());
        }

        return baos.toByteArray();
    }

    /**
     * Generate PDF from HTML-like content with basic formatting
     */
    public byte[] generatePdfFromFormattedContent(Map<String, Object> content) throws Exception {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();

        try (PdfWriter writer = new PdfWriter(baos);
                PdfDocument pdf = new PdfDocument(writer);
                Document document = new Document(pdf)) {

            pdf.setDefaultPageSize(PageSize.A4);

            // Process content map
            for (Map.Entry<String, Object> entry : content.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();

                switch (key.toLowerCase()) {
                    case "title":
                        addTitle(document, value.toString());
                        break;
                    case "subtitle":
                        addSubtitle(document, value.toString());
                        break;
                    case "paragraph":
                        addParagraph(document, value.toString());
                        break;
                    case "table":
                        if (value instanceof Map) {
                            @SuppressWarnings("unchecked")
                            Map<String, Object> tableData = (Map<String, Object>) value;
                            addTableFromMap(document, tableData);
                        }
                        break;
                    default:
                        addParagraph(document, key + ": " + value.toString());
                }
            }

        } catch (Exception e) {
            logger.error("Error generating PDF from formatted content", e);
            throw new Exception("Failed to generate PDF from formatted content: " + e.getMessage());
        }

        return baos.toByteArray();
    }

    // File Operations

    /**
     * Save PDF to file system
     */
    public String savePdfToFile(byte[] pdfData, String filename) throws IOException {
        // Ensure output directory exists
        Path outputPath = Paths.get(outputDirectory);
        if (!Files.exists(outputPath)) {
            Files.createDirectories(outputPath);
        }

        // Generate full file path
        String fullFilename = filename.endsWith(".pdf") ? filename : filename + ".pdf";
        Path filePath = outputPath.resolve(fullFilename);

        // Write file
        Files.write(filePath, pdfData);

        logger.info("PDF saved to: {}", filePath.toString());
        return filePath.toString();
    }

    /**
     * Generate and save PDF from text
     */
    public String generateAndSavePdf(String content, String title, String filename) throws Exception {
        byte[] pdfData = generatePdfFromText(content, title);
        return savePdfToFile(pdfData, filename);
    }

    // Export/Download Methods

    /**
     * Create ResponseEntity for PDF download
     */
    public ResponseEntity<Resource> createPdfDownloadResponse(byte[] pdfData, String filename) {
        String downloadFilename = filename.endsWith(".pdf") ? filename : filename + ".pdf";

        ByteArrayResource resource = new ByteArrayResource(pdfData);

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + downloadFilename + "\"")
                .contentType(MediaType.APPLICATION_PDF)
                .contentLength(pdfData.length)
                .body(resource);
    }

    /**
     * Create ResponseEntity for PDF inline view
     */
    public ResponseEntity<Resource> createPdfInlineResponse(byte[] pdfData, String filename) {
        String viewFilename = filename.endsWith(".pdf") ? filename : filename + ".pdf";

        ByteArrayResource resource = new ByteArrayResource(pdfData);

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + viewFilename + "\"")
                .contentType(MediaType.APPLICATION_PDF)
                .contentLength(pdfData.length)
                .body(resource);
    }

    // Utility Methods

    /**
     * Read PDF file and return as byte array
     */
    public byte[] readPdfFile(String filePath) throws IOException {
        Path path = Paths.get(filePath);
        if (!Files.exists(path)) {
            throw new FileNotFoundException("PDF file not found: " + filePath);
        }
        return Files.readAllBytes(path);
    }

    /**
     * Delete PDF file
     */
    public boolean deletePdfFile(String filePath) {
        try {
            Path path = Paths.get(filePath);
            return Files.deleteIfExists(path);
        } catch (IOException e) {
            logger.error("Error deleting PDF file: {}", filePath, e);
            return false;
        }
    }

    /**
     * List all PDF files in output directory
     */
    public List<String> listPdfFiles() throws IOException {
        Path outputPath = Paths.get(outputDirectory);
        if (!Files.exists(outputPath)) {
            return List.of();
        }

        return Files.list(outputPath)
                .filter(path -> path.toString().toLowerCase().endsWith(".pdf"))
                .map(path -> path.getFileName().toString())
                .toList();
    }

    // Helper Methods for Form Processing

    private String getFieldType(PdfFormField field) {
        if (field instanceof PdfTextFormField) {
            PdfTextFormField textField = (PdfTextFormField) field;
            if (textField.isPassword()) {
                return "PASSWORD";
            } else if (textField.isMultiline()) {
                return "TEXTAREA";
            } else {
                return "TEXT";
            }
        } else if (field instanceof PdfChoiceFormField) {
            PdfChoiceFormField choiceField = (PdfChoiceFormField) field;
            return choiceField.isCombo() ? "COMBOBOX" : "LISTBOX";
        } else if (field instanceof PdfButtonFormField) {
            return "BUTTON";
        } else if (field instanceof PdfSignatureFormField) {
            return "SIGNATURE";
        } else {
            return "UNKNOWN";
        }
    }

    private List<String> getChoiceOptions(PdfChoiceFormField choiceField) {
        List<String> options = new ArrayList<>();
        PdfArray optionArray = choiceField.getOptions();

        if (optionArray != null) {
            for (int i = 0; i < optionArray.size(); i++) {
                PdfObject option = optionArray.get(i);

                if (option.isArray()) {
                    // Option is an array [export_value, display_value]
                    PdfArray optionPair = (PdfArray) option;
                    if (optionPair.size() > 1) {
                        // Use display value (second element)
                        PdfObject displayValue = optionPair.get(1);
                        if (displayValue.isString()) {
                            options.add(((PdfString) displayValue).getValue());
                        }
                    } else if (!optionPair.isEmpty()) {
                        // Use first element if only one exists
                        PdfObject value = optionPair.get(0);
                        if (value.isString()) {
                            options.add(((PdfString) value).getValue());
                        }
                    }
                } else if (option.isString()) {
                    // Option is a simple string
                    options.add(((PdfString) option).getValue());
                }
            }
        }

        return options;
    }

    private void validateRequiredFields(Map<String, Map<String, Object>> fieldInfo, Map<String, String> formData)
            throws Exception {
        List<String> missingFields = new ArrayList<>();

        for (Map.Entry<String, Map<String, Object>> entry : fieldInfo.entrySet()) {
            String fieldName = entry.getKey();
            Map<String, Object> info = entry.getValue();

            Boolean isRequired = (Boolean) info.get("isRequired");
            if (isRequired != null && isRequired) {
                String value = formData.get(fieldName);
                if (value == null || value.trim().isEmpty()) {
                    missingFields.add(fieldName);
                }
            }
        }

        if (!missingFields.isEmpty()) {
            throw new Exception("Missing required fields: " + String.join(", ", missingFields));
        }
    }

    private void validateFieldData(Map<String, Map<String, Object>> fieldInfo, Map<String, String> formData)
            throws Exception {
        for (Map.Entry<String, String> entry : formData.entrySet()) {
            String fieldName = entry.getKey();
            String fieldValue = entry.getValue();

            Map<String, Object> info = fieldInfo.get(fieldName);
            if (info == null) {
                logger.warn("Field {} not found in PDF form", fieldName);
                continue;
            }

            String fieldType = (String) info.get("fieldType");

            // Validate based on field type
            switch (fieldType) {
                case "TEXT":
                case "TEXTAREA":
                    Integer maxLength = (Integer) info.get("maxLength");
                    if (maxLength != null && maxLength > 0 && fieldValue.length() > maxLength) {
                        throw new Exception("Field " + fieldName + " exceeds maximum length of " + maxLength);
                    }
                    break;
                case "COMBOBOX":
                case "LISTBOX":
                    @SuppressWarnings("unchecked")
                    List<String> options = (List<String>) info.get("options");
                    if (options != null && !options.isEmpty() && !options.contains(fieldValue)) {
                        throw new Exception("Field " + fieldName + " contains invalid option: " + fieldValue);
                    }
                    break;
            }
        }
    }

    private void addTitle(Document document, String title) throws IOException {
        PdfFont titleFont = PdfFontFactory.createFont();
        Paragraph titleParagraph = new Paragraph(title)
                .setFont(titleFont)
                .setFontSize(18)
                .setTextAlignment(TextAlignment.CENTER)
                .setMarginBottom(20);
        document.add(titleParagraph);
    }

    private void addSubtitle(Document document, String subtitle) throws IOException {
        PdfFont subtitleFont = PdfFontFactory.createFont();
        Paragraph subtitleParagraph =
                new Paragraph(subtitle).setFont(subtitleFont).setFontSize(14).setMarginBottom(15);
        document.add(subtitleParagraph);
    }

    private void addParagraph(Document document, String text) throws IOException {
        PdfFont paragraphFont = PdfFontFactory.createFont();
        Paragraph paragraph =
                new Paragraph(text).setFont(paragraphFont).setFontSize(12).setMarginBottom(10);
        document.add(paragraph);
    }

    @SuppressWarnings("unchecked")
    private void addTableFromMap(Document document, Map<String, Object> tableData) throws IOException {
        List<String> headers = (List<String>) tableData.get("headers");
        List<List<String>> data = (List<List<String>>) tableData.get("data");

        if (headers != null && data != null) {
            Table table = new Table(UnitValue.createPercentArray(headers.size()));
            table.setWidth(UnitValue.createPercentValue(100));

            // Add headers
            PdfFont headerFont = PdfFontFactory.createFont();
            for (String header : headers) {
                Cell headerCell = new Cell()
                        .add(new Paragraph(header))
                        .setFont(headerFont)
                        .setBackgroundColor(ColorConstants.LIGHT_GRAY);
                table.addHeaderCell(headerCell);
            }

            // Add data
            PdfFont dataFont = PdfFontFactory.createFont();
            for (List<String> row : data) {
                for (String cellData : row) {
                    Cell dataCell = new Cell()
                            .add(new Paragraph(cellData != null ? cellData : ""))
                            .setFont(dataFont)
                            .setFontSize(10);
                    table.addCell(dataCell);
                }
            }

            document.add(table);
        }
    }

    // Configuration Methods

    /**
     * Get current output directory
     */
    public String getOutputDirectory() {
        return outputDirectory;
    }

    /**
     * Set output directory
     */
    public void setOutputDirectory(String outputDirectory) {
        this.outputDirectory = outputDirectory;
    }

    /**
     * Validate PDF data
     */
    public boolean isValidPdf(byte[] pdfData) {
        try (ByteArrayInputStream bais = new ByteArrayInputStream(pdfData);
                PdfDocument pdf = new PdfDocument(new com.itextpdf.kernel.pdf.PdfReader(bais))) {
            return pdf.getNumberOfPages() > 0;
        } catch (Exception e) {
            logger.warn("Invalid PDF data", e);
            return false;
        }
    }
}
