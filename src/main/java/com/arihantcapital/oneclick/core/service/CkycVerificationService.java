package com.arihantcapital.oneclick.core.service;

import com.arihantcapital.oneclick.OneclickProperties;
import com.arihantcapital.oneclick.core.client.ckyc.CkycVerificationClientImpl;
import com.arihantcapital.oneclick.core.dto.ckyc.CkycVerifyRQ;
import com.arihantcapital.oneclick.core.utils.CkycSignUtil;
import com.arihantcapital.oneclick.enums.RandomNumberGenerationMethod;
import com.arihantcapital.oneclick.utils.RandomNumberUtils;
import java.io.IOException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.UnrecoverableEntryException;
import java.security.cert.CertificateException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service("ckycService")
public class CkycVerificationService {
    private final OneclickProperties.CkycConfig config;

    private CkycVerificationClientImpl ckycVerificationServiceClient;
    private CkycSignUtil ckycSignUtil;

    public CkycVerificationService(OneclickProperties.CkycConfig config)
            throws UnrecoverableEntryException, CertificateException, KeyStoreException, IOException,
                    NoSuchAlgorithmException {
        this.config = config;

        CkycVerificationClientImpl.ClientConfig clientConfig = new CkycVerificationClientImpl.ClientConfig(config);
        this.ckycVerificationServiceClient = new CkycVerificationClientImpl(clientConfig);
        log.info("CKYC Service initialized with client config: {}", clientConfig);
        this.ckycSignUtil = new CkycSignUtil(
                clientConfig.getApiVersion(),
                clientConfig.getKeyStoreType(),
                clientConfig.getFiCode(),
                clientConfig.getCersaiPublicKeyFilePath(),
                clientConfig.getFiCertificateKeyStoreFilePath(),
                clientConfig.getFiCertificateKeyStoreFilePassword());
    }

    // Your CKYC service methods here
    public String ckycVerify(CkycVerifyRQ verifyRQ) {
        try {

            String requestId = verifyRQ.getRequestId();

            log.info("Received CKYC verification request: {}", verifyRQ);

            // Check if verification request has request id
            // If it does, log the request id
            // If it doesn't, generate a new request id
            if (verifyRQ.getRequestId() == null || verifyRQ.getRequestId().isEmpty()) {
                requestId = RandomNumberUtils.generateRequestId(RandomNumberGenerationMethod.UUID);
                log.info("Request id not provided, generated new request id: {}", requestId);
            }

            // Request payload
            String payload =
                    ckycSignUtil.getCkycVerifySignedRequest(verifyRQ.getIdType(), verifyRQ.getIdNumber(), requestId);
            log.info("CKYC verification request payload: {}", payload);

            // Send Request
            String responseString = ckycVerificationServiceClient.verifyCkyc(requestId, payload);

            // Decrypt Response
            String decryptedResponse = ckycSignUtil.getCkycVerifyDecryptedResponse(responseString);
            log.info("CKYC verification successful: {}", decryptedResponse);

            return decryptedResponse;
        } catch (Exception e) {
            log.error("CKYC verification failed: {}", e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    public String ckycDownload() {
        return "";
    }

    public String ckycValidateOtp() {
        return "";
    }
}
