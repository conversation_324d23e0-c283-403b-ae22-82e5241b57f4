package com.arihantcapital.oneclick.core.service;

import com.arihantcapital.oneclick.OneclickProperties;
import com.arihantcapital.oneclick.core.client.bse.starmutualfund.BseStarMutualFundClientImpl;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class BseStarMutualFundService {
    private final OneclickProperties.BseStarMutualFundConfig config;
    private BseStarMutualFundClientImpl bseStarMutualFundClient;

    public BseStarMutualFundService(OneclickProperties.BseStarMutualFundConfig config) {
        this.config = config;
        BseStarMutualFundClientImpl.ClientConfig clientConfig = new BseStarMutualFundClientImpl.ClientConfig(config);
        this.bseStarMutualFundClient = new BseStarMutualFundClientImpl(clientConfig);
        log.info("BSE Star Mutual Fund Service initialized with client config: {}", clientConfig);
    }

    public String registration(String regnType, String param) {
        try {
            return bseStarMutualFundClient.registration(regnType, param);
        } catch (Exception e) {
            log.error(e.getMessage());
            return null;
        }
    }

    public String registrationV183(String regnType, String param) {
        try {
            return bseStarMutualFundClient.registrationV183(regnType, param);
        } catch (Exception e) {
            log.error(e.getMessage());
            return null;
        }
    }

    public String nomineeRegistrationV56(String regnType, String param) {
        try {
            return bseStarMutualFundClient.nomineeRegistrationV56(regnType, param);
        } catch (Exception e) {
            log.error(e.getMessage());
            return null;
        }
    }
}
