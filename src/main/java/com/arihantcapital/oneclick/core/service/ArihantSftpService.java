package com.arihantcapital.oneclick.core.service;

import com.arihantcapital.oneclick.OneclickProperties;
import com.arihantcapital.oneclick.core.client.arihant.sftp.ArihantSftpClientImpl;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ArihantSftpService {

    private final OneclickProperties.ArihantSftpConfig config;
    private final ArihantSftpClientImpl arihantSftpClient;

    public ArihantSftpService(OneclickProperties.ArihantSftpConfig config) {
        this.config = config;
        ArihantSftpClientImpl.ClientConfig clientConfig = new ArihantSftpClientImpl.ClientConfig(config);
        this.arihantSftpClient = new ArihantSftpClientImpl(clientConfig);
        log.info("Arihant SFTP Service initialized with client config: {}", clientConfig);
    }
}
