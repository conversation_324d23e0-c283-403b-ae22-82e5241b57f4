package com.arihantcapital.oneclick.core.client.korp;

import com.arihantcapital.oneclick.OneclickProperties;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.validation.constraints.NotBlank;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

@Slf4j
@Service(value = "korpClient")
public class KorpClientImpl {

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();
    private KorpClientImpl.ClientConfig clientConfig = new ClientConfig();

    public KorpClientImpl(KorpClientImpl.ClientConfig clientConfig) {
        this.clientConfig = clientConfig;
        log.info("KORP Client Config: {}", clientConfig);
    }

    public KorpClientImpl() {}

    public String authenticate() throws JsonProcessingException {
        String route = ClientRoutes.AUTHENTICATE;

        // Request Payload
        // Create form data as a string
        String formData = "UserName=" + clientConfig.getUsername() + "&Password="
                + URLEncoder.encode(clientConfig.getPassword(), StandardCharsets.UTF_8) + "&Grant_type="
                + clientConfig.getGrantType();

        // Http Headers
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        log.info("KORP Authenticate Request Headers: {}", httpHeaders);
        log.info("KORP Authenticate Request Body: {}", formData);

        // Sending Request
        HttpEntity<String> requestEntity = new HttpEntity<>(formData, httpHeaders);
        ResponseEntity<String> response =
                restTemplate.postForEntity(clientConfig.getBaseUrl() + route, requestEntity, String.class);
        String responseString = response.getBody();
        log.info("KORP Authenticate Response: {}", responseString);

        JsonNode responseJson = objectMapper.readTree(responseString);
        log.info("KORP Authenticate Response Json: {}", responseJson);

        return responseJson.get("access_token").asText();
    }

    public JsonNode getClientMaster(String clientCode, String clientType) throws JsonProcessingException {
        String route = ClientRoutes.GET_CLIENT_MASTER;

        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(clientConfig.getBaseUrl() + route)
                .queryParam("Code", clientCode)
                .queryParam("ClientType", clientType);
        String url = builder.build().encode().toUriString();

        // Http Headers
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.add("Authorization", "Bearer " + this.authenticate());
        httpHeaders.add("FIRMID", "1001");

        log.info("KORP Get Client Master Request URL: {}", url);
        log.info("KORP Get Client Master Request Headers: {}", httpHeaders);

        // Sending Request
        HttpEntity<String> requestEntity = new HttpEntity<>(httpHeaders);
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, requestEntity, String.class);
        String responseString = response.getBody();
        log.info("KORP Get Client Master Response: {}", responseString);

        JsonNode responseJson = objectMapper.readTree(responseString);
        log.info("KORP Get Client Master Response Json: {}", responseJson);

        return responseJson;
    }

    public JsonNode getBranchMaster(String branchCode) throws JsonProcessingException {
        String route = ClientRoutes.GET_BRANCH_MASTER;

        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(clientConfig.getBaseUrl() + route)
                .queryParam("Code", branchCode);
        String url = builder.build().encode().toUriString();

        // Http Headers
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.add("Authorization", "Bearer " + this.authenticate());
        httpHeaders.add("FIRMID", "1001");

        log.info("KORP Get Branch Master Request URL: {}", url);
        log.info("KORP Get Branch Master Request Headers: {}", httpHeaders);

        // Sending Request
        HttpEntity<String> requestEntity = new HttpEntity<>(httpHeaders);
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, requestEntity, String.class);
        String responseString = response.getBody();
        log.info("KORP Get Branch Master Response: {}", responseString);

        JsonNode responseJson = objectMapper.readTree(responseString);
        log.info("KORP Get Branch Master Response Json: {}", responseJson);

        return responseJson;
    }

    @Data
    public static class ClientConfig {
        @NotBlank(message = "Base URL is required") private String baseUrl = "https://korp.knowyourclient.in";

        @NotBlank(message = "Username is required") private String username = "arihantcapital";

        @NotBlank(message = "Password is required") private String password = "arihant@123";

        @NotBlank(message = "Grant Type is required") private String grantType = "password";

        public ClientConfig(String baseUrl, String username, String password, String grantType) {
            this.baseUrl = baseUrl;
            this.username = username;
            this.password = password;
            this.grantType = grantType;
        }

        public ClientConfig(OneclickProperties.KorpConfig config) {
            this.baseUrl = config.baseUrl();
            this.username = config.username();
            this.password = config.password();
            this.grantType = config.grantType();
        }

        public ClientConfig() {}
    }

    public static class ClientRoutes {
        public static final String AUTHENTICATE = "/api/token";
        public static final String GET_CLIENT_MASTER = "/api/Masters/GetOrionEKYCDetail/Get";
        public static final String GET_BRANCH_MASTER = "/api/Masters/Branch/Get";
    }
}
