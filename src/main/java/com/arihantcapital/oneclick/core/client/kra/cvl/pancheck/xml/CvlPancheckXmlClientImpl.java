package com.arihantcapital.oneclick.core.client.kra.cvl.pancheck.xml;

import com.arihantcapital.oneclick.OneclickProperties;
import com.arihantcapital.oneclick.enums.RandomNumberGenerationMethod;
import com.arihantcapital.oneclick.utils.RandomNumberUtils;
import com.arihantcapital.oneclick.utils.XmlUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import jakarta.validation.constraints.NotBlank;
import java.util.Optional;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Service(value = "cvlPancheckXmlClient")
public class CvlPancheckXmlClientImpl {

    // Instance variables
    private final String version = "";
    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final XmlMapper xmlMapper = new XmlMapper();

    private CvlPancheckXmlClientImpl.ClientConfig clientConfig = new ClientConfig();

    public CvlPancheckXmlClientImpl(CvlPancheckXmlClientImpl.ClientConfig clientConfig) {
        this.clientConfig = clientConfig;
        log.info("Cvl Pancheck Xml Client Config: {}", clientConfig);
    }

    public CvlPancheckXmlClientImpl() {}

    public JsonNode getToken(String passKey) {
        String baseUrl = clientConfig.getBaseUrl();
        String serviceUrl = baseUrl + "/CVLPanInquiry.svc";
        String soapAction = baseUrl + ClientSoapActions.GET_TOKEN;
        log.info("Initiating GetToken Xml (RestTemplate) for SOAPAction: {}, passKey: {}", soapAction, passKey);

        String soapPayload = String.format(
                "<Envelope xmlns='http://schemas.xmlsoap.org/soap/envelope/'><Body><GetPassword xmlns='%s'><webApi><password>%s</password><passKey>%s</passKey></webApi></GetPassword></Body></Envelope>",
                baseUrl, clientConfig.getPassword(), passKey);

        // HTTP Request Headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.TEXT_XML);
        headers.add("SOAPAction", soapAction);
        log.info("GetToken Xml Request Headers: {}", headers);
        log.info("GetToken Xml Request Payload: {}", soapPayload);

        // HTTP Request Body
        HttpEntity<String> requestEntity = new HttpEntity<>(soapPayload, headers);

        // HTTP Request Send
        ResponseEntity<String> response = restTemplate.postForEntity(serviceUrl, requestEntity, String.class);
        String responseString = response.getBody();
        log.info("GetToken Xml Response: {}", response);

        // Example: Extract specific tags from the SOAP response
        // Create Json Node with property token
        ObjectMapper objectMapper = new ObjectMapper();
        if (responseString != null) {
            // Extract APP_GET_PASS from SOAP response using recursive search
            Optional<String> appGetPass = XmlUtils.findFirstTagRecursively(responseString, "APP_GET_PASS");
            if (appGetPass.isPresent()) {
                log.info("Extracted APP_GET_PASS: {}", appGetPass.get());
                // You can return this value or process it further
                JsonNode jsonNode = objectMapper
                        .createObjectNode()
                        .put("token", appGetPass.get())
                        .put("passKey", passKey);
                return jsonNode;
            }
        }

        throw new RuntimeException("GetToken Xml Response is null");
    }

    public JsonNode getPanStatus(String panNumber) {
        String baseUrl = clientConfig.getBaseUrl();
        String serviceUrl = baseUrl + "/CVLPanInquiry.svc";
        String soapAction = baseUrl + ClientSoapActions.GET_PAN_STATUS;
        log.info("Initiating GetPanStatus Xml (RestTemplate) for SOAPAction: {}, panNumber: {}", soapAction, panNumber);

        // Get Token
        String passKey = RandomNumberUtils.generateRequestId(RandomNumberGenerationMethod.UUID);
        JsonNode token = this.getToken(passKey);
        String password = token.get("token").asText();
        String soapPayload = String.format(
                "<Envelope xmlns='http://schemas.xmlsoap.org/soap/envelope/'><Body><GetPanStatus xmlns='%s'><webApi><pan>%s</pan><userName>%s</userName><posCode>%s</posCode><password>%s</password><passKey>%s</passKey></webApi></GetPanStatus></Body></Envelope>",
                baseUrl, panNumber, clientConfig.getUsername(), clientConfig.getPosCode(), password, passKey);

        // HTTP Request Headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.TEXT_XML);
        headers.add("SOAPAction", soapAction);
        log.info("GetPanStatus Xml Request Headers: {}", headers);
        log.info("GetPanStatus Xml Request Payload: {}", soapPayload);

        // HTTP Request Body
        HttpEntity<String> requestEntity = new HttpEntity<>(soapPayload, headers);

        // HTTP Request Send
        ResponseEntity<String> response = restTemplate.postForEntity(serviceUrl, requestEntity, String.class);
        String responseString = response.getBody();
        log.info("GetPanStatus Xml Response: {}", responseString);

        JsonNode jsonNode = XmlUtils.extractNestedStructureAsJsonNodeClean(responseString, "APP_RES_ROOT");
        log.info("GetPanStatus Xml Response Json: {}", jsonNode);
        return jsonNode;
    }

    public JsonNode solicitPANDetailsFetchALLKRA(String fetchType, String kraCode, String panNumber, String dob) {
        String baseUrl = clientConfig.getBaseUrl();
        String serviceUrl = baseUrl + "/CVLPanInquiry.svc";
        String soapAction = baseUrl + ClientSoapActions.SOLICIT_PAN_DETAILS_FETCH_ALL_KRA;
        log.info(
                "Initiating SolicitPANDetailsFetchALLKRA Xml (RestTemplate) for SOAPAction: {}, KraCode: {}, panNumber: {}, dob: {}",
                soapAction,
                kraCode,
                panNumber,
                dob);

        // Get Token
        String passKey = RandomNumberUtils.generateRequestId(RandomNumberGenerationMethod.UUID);
        JsonNode token = this.getToken(passKey);
        String password = token.get("token").asText();
        String soapPayload = String.format(
                "<Envelope xmlns='http://schemas.xmlsoap.org/soap/envelope/'><Body><SolicitPANDetailsFetchALLKRA xmlns='%s'><webApi><inputXml><![CDATA[<APP_REQ_ROOT><APP_PAN_INQ><APP_PAN_NO>%s</APP_PAN_NO><APP_DOB_INCORP>%s</APP_DOB_INCORP><APP_POS_CODE>%s</APP_POS_CODE><APP_RTA_CODE>%s</APP_RTA_CODE><APP_KRA_CODE>%s</APP_KRA_CODE><FETCH_TYPE>%s</FETCH_TYPE></APP_PAN_INQ></APP_REQ_ROOT>]]></inputXml><userName>%s</userName><posCode>%s</posCode><password>%s</password><passKey>%s</passKey></webApi></SolicitPANDetailsFetchALLKRA></Body></Envelope>",
                baseUrl,
                panNumber,
                dob,
                clientConfig.getPosCode(),
                clientConfig.getRtaCode(),
                kraCode,
                "I",
                clientConfig.getUsername(),
                clientConfig.getPosCode(),
                password,
                passKey);

        // HTTP Request Headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.TEXT_XML);
        headers.add("SOAPAction", soapAction);
        log.info("SolicitPANDetailsFetchALLKRA Xml Request Headers: {}", headers);
        log.info("SolicitPANDetailsFetchALLKRA Xml Request Payload: {}", soapPayload);

        // HTTP Request Body
        HttpEntity<String> requestEntity = new HttpEntity<>(soapPayload, headers);

        // HTTP Request Send
        ResponseEntity<String> response = restTemplate.postForEntity(serviceUrl, requestEntity, String.class);
        String responseString = response.getBody();
        log.info("SolicitPANDetailsFetchALLKRA Xml Response: {}", responseString);

        JsonNode appType = XmlUtils.extractNestedStructureAsJsonNodeClean(responseString, "APP_TYPE");
        log.info("APP_TYPE: {}", appType);
        boolean a = appType.asText().equals("I") || appType.asText().equals("NI");
        boolean b = responseString.contains(panNumber) && responseString.contains(kraCode);
        switch (kraCode) {
            case "CVLKRA":
                log.info("Extracting CVLKRA Response Json: {}", responseString);
                // Check if ROOT tag exists
                if (a && b) {
                    JsonNode jsonNode = XmlUtils.extractNestedStructureAsJsonNodeClean(responseString, "ROOT");
                    log.info("SolicitPANDetailsFetchALLKRA Xml CVLKRA Response Json: {}", jsonNode);
                    return jsonNode;
                }
            case "DOTEX":
                log.info("Extracting DOTEX Response Json: {}", responseString);
                // Check if ROOT tag exists
                if (a && b) {
                    JsonNode jsonNode = XmlUtils.extractNestedStructureAsJsonNodeClean(responseString, "ROOT");
                    log.info("SolicitPANDetailsFetchALLKRA Xml DOTEX Response Json: {}", jsonNode);
                    return jsonNode;
                }
            case "NDML":
                log.info("Extracting NDML Response Json: {}", responseString);
                // Check if ROOT tag exists
                if (a && b) {
                    JsonNode jsonNode = XmlUtils.extractNestedStructureAsJsonNodeClean(responseString, "ROOT");
                    log.info("SolicitPANDetailsFetchALLKRA Xml NDML Response Json: {}", jsonNode);
                    return jsonNode;
                }
            case "CAMS":
                log.info("Extracting CAMS Response Json: {}", responseString);
                // Check if ROOT tag exists
                if (a && b) {
                    JsonNode jsonNode = XmlUtils.extractNestedStructureAsJsonNodeClean(responseString, "ROOT");
                    log.info("SolicitPANDetailsFetchALLKRA Xml CAMS Response Json: {}", jsonNode);
                    return jsonNode;
                }
            case "KARVY":
                log.info("Extracting KARVY Response Json: {}", responseString);
                // Check if ROOT tag exists
                if (a && b) {
                    JsonNode jsonNode = XmlUtils.extractNestedStructureAsJsonNodeClean(responseString, "ROOT");
                    log.info("SolicitPANDetailsFetchALLKRA Xml KARVY Response Json: {}", jsonNode);
                    return jsonNode;
                }
            default:
                throw new IllegalArgumentException("Invalid kraCode: " + kraCode);
        }
    }

    public JsonNode insertUpdateKYCRecord(String payload) {
        String baseUrl = clientConfig.getBaseUrl();
        String soapAction = baseUrl + ClientSoapActions.INSERT_UPDATE_KYC_RECORD;
        log.info("Initiating InsertUpdateKYCRecord Xml (RestTemplate) for SOAPAction: {}", soapAction);
        return null;
    }

    @Data
    public static class ClientConfig {

        @NotBlank(message = "Base URL is required") private String baseUrl = "https://pancheck.www.kracvl.com";

        @NotBlank(message = "Username is required") private String username = "NEWEKYC";

        @NotBlank(message = "Password is required") private String password = "acml@1234";

        @NotBlank(message = "POS Code is required") private String posCode = "1100043000";

        @NotBlank(message = "RTA Code is required") private String rtaCode = "1100043000";

        // Constructor for default configuration
        public ClientConfig(String baseUrl, String username, String password, String posCode, String rtaCode) {
            this.baseUrl = baseUrl;
            this.username = username;
            this.password = password;
            this.posCode = posCode;
            this.rtaCode = rtaCode;
        }

        public ClientConfig(OneclickProperties.KraConfig.CvlConfig.PancheckConfig.XmlConfig config) {
            this.baseUrl = config.baseUrl();
            this.username = config.username();
            this.password = config.password();
            this.posCode = config.posCode();
            this.rtaCode = config.rtaCode();
        }

        public ClientConfig() {}
    }

    // Service routes
    public static class ClientSoapActions {
        public static final String GET_TOKEN = "/ICVLPanInquiry/GetPassword";
        public static final String GET_PAN_STATUS = "/ICVLPanInquiry/GetPanStatus";
        public static final String SOLICIT_PAN_DETAILS_FETCH_ALL_KRA = "/ICVLPanInquiry/SolicitPANDetailsFetchALLKRA";
        public static final String INSERT_UPDATE_KYC_RECORD = "/ICVLPanInquiry/InsertUpdateKYCRecord";
    }
}
