package com.arihantcapital.oneclick.core.client.odin;

import com.arihantcapital.oneclick.OneclickProperties;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service(value = "odinUumClient")
public class OdinUumClientImpl {

    private OdinUumClientImpl.ClientConfig clientConfig = new ClientConfig();

    public OdinUumClientImpl(OdinUumClientImpl.ClientConfig clientConfig) {
        this.clientConfig = clientConfig;
        log.info("ODIN UUM Client Config: {}", clientConfig);
    }

    public String authenticate() {
        return "";
    }

    public OdinUumClientImpl() {}

    @Data
    public static class ClientConfig {
        @NotBlank(message = "Base URL is required") private String baseUrl = "http://**************:9898/usercreationwcfservice/UserCreation.svc/soap";

        @NotBlank(message = "Username is required") private String username = "RESERVEDP4";

        @NotBlank(message = "Password is required") private String password = "ftodin1";

        @NotBlank(message = "IP Address is required") private String ipAddress = "127.0.0.1";

        public ClientConfig(String baseUrl, String username, String password, String ipAddress) {
            this.baseUrl = baseUrl;
            this.username = username;
            this.password = password;
            this.ipAddress = ipAddress;
        }

        public ClientConfig(OneclickProperties.OdinUumConfig config) {
            this.baseUrl = config.baseUrl();
            this.username = config.username();
            this.password = config.password();
            this.ipAddress = config.ipAddress();
        }

        public ClientConfig() {}
    }

    public static class ClientRoutes {
        public static final String AUTHENTICATE = "/oauth/token";
    }
}
