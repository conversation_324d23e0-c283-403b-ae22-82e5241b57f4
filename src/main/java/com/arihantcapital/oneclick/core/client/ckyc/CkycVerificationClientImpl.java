package com.arihantcapital.oneclick.core.client.ckyc;

import com.arihantcapital.oneclick.OneclickProperties;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Service(value = "ckycVerificationClient")
public class CkycVerificationClientImpl {

    private final String version = "";
    private final RestTemplate restTemplate = new RestTemplate();
    private final XmlMapper xmlMapper = new XmlMapper();

    private CkycVerificationClientImpl.ClientConfig clientConfig = new ClientConfig();

    public CkycVerificationClientImpl(CkycVerificationClientImpl.ClientConfig clientConfig) {
        this.clientConfig = clientConfig;
        log.info("Ckyc Verification Client Config: {}", clientConfig);
    }

    public CkycVerificationClientImpl() {}

    public String verifyCkyc(String requestId, String payload) {

        String url = clientConfig.getBaseUrl() + ClientRoutes.VERIFY;
        log.info("Initiating CKYC verify (RestTemplate) for requestId: {}, URl: {}", requestId, url);

        // HTTP Request Headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_XML);

        // HTTP Request Body
        HttpEntity<String> requestEntity = new HttpEntity<>(payload, headers);

        // HTTP Request Send
        ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
        String responseString = response.getBody();
        log.info("CKYC verify response: {}", responseString);

        return responseString;
    }

    public String downloadCkyc(String requestId, String payload) {
        String url = clientConfig.getBaseUrl() + ClientRoutes.DOWNLOAD;
        log.info("Initiating CKYC download (RestTemplate) for requestId: {}, URl: {}", requestId, url);
        return "";
    }

    public String validateOtp(String requestId, String otp) {
        String url = clientConfig.getBaseUrl() + ClientRoutes.VALIDATE_OTP;
        log.info("Initiating CKYC ValidateOTP (RestTemplate) for requestId: {}, URl: {}", requestId, url);
        return "";
    }

    @Data
    public static class ClientConfig {
        @NotBlank(message = "Base URL is required") private String baseUrl = "https://www.ckycindia.in";

        @NotBlank(message = "API Version is required") private String apiVersion = "1.3";

        @NotBlank(message = "FI Code is required") private String fiCode = "IN0888";

        @NotBlank(message = "Key Store Type is required") private String keyStoreType = "PKCS12";

        @NotBlank(message = "Cersai Public Key File Path is required") private String cersaiPublicKeyFilePath = "ckyc_CersaiSignPublicKey.cer";

        @NotBlank(message = "FI Certificate Key Store File Path is required") private String fiCertificateKeyStoreFilePath = "ckyc_rohit.pfx";

        @NotBlank(message = "FI Certificate Key Store File Password is required") private String fiCertificateKeyStoreFilePassword = "roh123";

        private String fiCertificatePrivateKeyAlias = "te-9d800711-1c56-4a7e-8c77-d3c22ffe5da0";

        public ClientConfig(
                String baseUrl,
                String apiVersion,
                String fiCode,
                String keyStoreType,
                String cersaiPublicKeyFilePath,
                String fiCertificateKeyStoreFilePath,
                String fiCertificateKeyStoreFilePassword,
                String fiCertificatePrivateKeyAlias) {
            this.baseUrl = baseUrl;
            this.apiVersion = apiVersion;
            this.fiCode = fiCode;
            this.keyStoreType = keyStoreType;
            this.cersaiPublicKeyFilePath = cersaiPublicKeyFilePath;
            this.fiCertificateKeyStoreFilePath = fiCertificateKeyStoreFilePath;
            this.fiCertificateKeyStoreFilePassword = fiCertificateKeyStoreFilePassword;
            this.fiCertificatePrivateKeyAlias = fiCertificatePrivateKeyAlias;
        }

        public ClientConfig(OneclickProperties.CkycConfig config) {
            this.baseUrl = config.baseUrl();
            this.apiVersion = config.apiVersion();
            this.fiCode = config.fiCode();
            this.keyStoreType = config.keyStoreType();
            this.cersaiPublicKeyFilePath = config.cersaiPublicKeyFilePath();
            this.fiCertificateKeyStoreFilePath = config.fiCertificateKeyStoreFilePath();
            this.fiCertificateKeyStoreFilePassword = config.fiCertificateKeyStoreFilePassword();
            this.fiCertificatePrivateKeyAlias = config.fiCertificatePrivateKeyAlias();
        }

        public ClientConfig() {}
    }

    // Service Routes
    public static class ClientRoutes {
        public static final String VERIFY = "/Search/ckycverificationservice/verify";
        public static final String DOWNLOAD = "/Search/ckycverificationservice/download";
        public static final String VALIDATE_OTP = "/Search/ckycverificationservice/ValidateOTP";
    }
}
