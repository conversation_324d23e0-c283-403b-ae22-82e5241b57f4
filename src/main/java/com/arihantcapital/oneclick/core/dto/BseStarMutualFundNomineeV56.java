package com.arihantcapital.oneclick.core.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class BseStarMutualFundNomineeV56 {
    @JsonProperty("ClientCode")
    private String clientCode;

    @JsonProperty("Nom_SOA")
    private String nomSoa;

    @JsonProperty("NominationOpt")
    private String nominationOpt;

    @JsonProperty("NominationAuthMode")
    private String nominationAuthMode;

    @JsonProperty("Nom1_Name")
    private String nom1Name;

    @JsonProperty("Nom1_Relationship")
    private String nom1Relationship;

    @JsonProperty("Nom1_App_Percent")
    private String nom1AppPercent;

    @JsonProperty("Nom1_MinorFlag")
    private String nom1MinorFlag;

    @JsonProperty("Nom1_Dob")
    private String nom1Dob;

    @JsonProperty("Nom1_Guardian")
    private String nom1Guardian;

    @JsonProperty("Nom1_GuardianPAN")
    private String nom1GuardianPan;

    @JsonProperty("Nom1_IdType")
    private String nom1IdType;

    @JsonProperty("Nom1_IdNo")
    private String nom1IdNo;

    @JsonProperty("Nom1_Email")
    private String nom1Email;

    @JsonProperty("Nom1_MobileNo")
    private String nom1MobileNo;

    @JsonProperty("Nom1_Add1")
    private String nom1Add1;

    @JsonProperty("Nom1_Add2")
    private String nom1Add2;

    @JsonProperty("Nom1_Add3")
    private String nom1Add3;

    @JsonProperty("Nom1_City")
    private String nom1City;

    @JsonProperty("Nom1_Pincode")
    private String nom1Pincode;

    @JsonProperty("Nom1_Country")
    private String nom1Country;

    @JsonProperty("Nom2_Name")
    private String nom2Name;

    @JsonProperty("Nom2_Relationship")
    private String nom2Relationship;

    @JsonProperty("Nom2_Appl_Percent")
    private String nom2ApplPercent;

    @JsonProperty("Nom2_MinorFlag")
    private String nom2MinorFlag;

    @JsonProperty("Nom2_Dob")
    private String nom2Dob;

    @JsonProperty("Nom2_Guardian")
    private String nom2Guardian;

    @JsonProperty("Nom2_GuardianPAN")
    private String nom2GuardianPan;

    @JsonProperty("Nom2_IdType")
    private String nom2IdType;

    @JsonProperty("Nom2_IdNo")
    private String nom2IdNo;

    @JsonProperty("Nom2_Email")
    private String nom2Email;

    @JsonProperty("Nom2_MobileNo")
    private String nom2MobileNo;

    @JsonProperty("Nom2_Add1")
    private String nom2Add1;

    @JsonProperty("Nom2_Add2")
    private String nom2Add2;

    @JsonProperty("Nom2_Add3")
    private String nom2Add3;

    @JsonProperty("Nom2_City")
    private String nom2City;

    @JsonProperty("Nom2_Pincode")
    private String nom2Pincode;

    @JsonProperty("Nom2_Country")
    private String nom2Country;

    @JsonProperty("Nom3_Name")
    private String nom3Name;

    @JsonProperty("Nom3_Relationship")
    private String nom3Relationship;

    @JsonProperty("Nom3_Appl_Percent")
    private String nom3ApplPercent;

    @JsonProperty("Nom3_MinorFlag")
    private String nom3MinorFlag;

    @JsonProperty("Nom3_Dob")
    private String nom3Dob;

    @JsonProperty("Nom3_Guardian")
    private String nom3Guardian;

    @JsonProperty("Nom3_GuardianPAN")
    private String nom3GuardianPan;

    @JsonProperty("Nom3_IdType")
    private String nom3IdType;

    @JsonProperty("Nom3_IdNo")
    private String nom3IdNo;

    @JsonProperty("Nom3_Email")
    private String nom3Email;

    @JsonProperty("Nom3_MobileNo")
    private String nom3MobileNo;

    @JsonProperty("Nom3_Add1")
    private String nom3Add1;

    @JsonProperty("Nom3_Add2")
    private String nom3Add2;

    @JsonProperty("Nom3_Add3")
    private String nom3Add3;

    @JsonProperty("Nom3_City")
    private String nom3City;

    @JsonProperty("Nom3_Pincode")
    private String nom3Pincode;

    @JsonProperty("Nom3_Country")
    private String nom3Country;
}
