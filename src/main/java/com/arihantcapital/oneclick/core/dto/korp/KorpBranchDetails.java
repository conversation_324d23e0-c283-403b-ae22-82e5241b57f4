package com.arihantcapital.oneclick.core.dto.korp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class KorpBranchDetails {

    @JsonProperty("FirmID")
    private String firmId;

    @JsonProperty("BranchID")
    private String branchId;

    @JsonProperty("BranchType")
    private String branchType;

    @JsonProperty("BranchStatus")
    private String branchStatus;

    @JsonProperty("BranchMainCode")
    private String branchMainCode;

    @JsonProperty("BranchName")
    private String branchName;

    @JsonProperty("OpenDate")
    private String openDate;

    @JsonProperty("CloseDate")
    private String closeDate;

    @JsonProperty("Region")
    private String region;

    @JsonProperty("Remisier")
    private String remisier;

    @JsonProperty("Manager")
    private String manager;

    @JsonProperty("TaxState")
    private String taxState;

    @JsonProperty("PaymentType")
    private String paymentType;

    @JsonProperty("TDSCategory")
    private String tdscategory;

    @JsonProperty("TDSSection")
    private String tdssection;

    @JsonProperty("BranchSubType")
    private String branchSubType;

    @JsonProperty("PANNo")
    private String panno;

    @JsonProperty("GSTRegistrationNo")
    private String gstregistrationNo;

    @JsonProperty("ReceiveBankCode")
    private String receiveBankCode;

    @JsonProperty("APPan")
    private String appan;

    @JsonProperty("ParentCode")
    private String parentCode;

    @JsonProperty("EKYCURL")
    private String ekycurl;

    @JsonProperty("CTCLBranch")
    private String ctclbranch;
}
