package com.arihantcapital.oneclick.core.dto;

import lombok.Data;

@Data
public class BseStarMutualFundClientV183 {
    private String clientCode;
    private String primaryHolderFirstName;
    private String primaryHolderMiddleName;
    private String primaryHolderLastName;
    private String taxStatus;
    private String gender;
    private String primaryHolderDobOrIncorporation;
    private String occupationCode;
    private String holdingNature;
    private String secondHolderFirstName;
    private String secondHolderMiddleName;
    private String secondHolderLastName;
    private String thirdHolderFirstName;
    private String thirdHolderMiddleName;
    private String thirdHolderLastName;
    private String secondHolderDob;
    private String thirdHolderDob;
    private String guardianFirstName;
    private String guardianMiddleName;
    private String guardianLastName;
    private String guardianDob;
    private String primaryHolderPanExempt;
    private String secondHolderPanExempt;
    private String thirdHolderPanExempt;
    private String guardianPanExempt;
    private String primaryHolderPan;
    private String secondHolderPan;
    private String thirdHolderPan;
    private String guardianPan;
    private String primaryHolderPanExemptCategory;
    private String secondHolderPanExemptCategory;
    private String thirdHolderPanExemptCategory;
    private String guardianPanExemptCategory;
    private String clientType;
    private String pms;
    private String defaultDp;
    private String cdslDpId;
    private String cdslClientId;
    private String cmbpId;
    private String nsdlDpId;
    private String nsdlClientId;
    private String accountType1;
    private String accountNo1;
    private String micrNo1;
    private String ifscCode1;
    private String defaultBankFlag1;
    private String accountType2;
    private String accountNo2;
    private String micrNo2;
    private String ifscCode2;
    private String defaultBankFlag2;
    private String accountType3;
    private String accountNo3;
    private String micrNo3;
    private String ifscCode3;
    private String defaultBankFlag3;
    private String accountType4;
    private String accountNo4;
    private String micrNo4;
    private String ifscCode4;
    private String defaultBankFlag4;
    private String accountType5;
    private String accountNo5;
    private String micrNo5;
    private String ifscCode5;
    private String defaultBankFlag5;
    private String chequeName;
    private String divPayMode;
    private String address1;
    private String address2;
    private String address3;
    private String city;
    private String state;
    private String pincode;
    private String country;
    private String resiPhone;
    private String resiFax;
    private String officePhone;
    private String officeFax;
    private String email;
    private String communicationMode;
    private String foreignAddress1;
    private String foreignAddress2;
    private String foreignAddress3;
    private String foreignAddressCity;
    private String foreignAddressPincode;
    private String foreignAddressState;
    private String foreignAddressCountry;
    private String foreignAddressResiPhone;
    private String foreignAddressResiFax;
    private String foreignAddressOfficePhone;
    private String foreignAddressOfficeFax;
    private String indianMobileNo;
    private String primaryHolderKycType;
    private String primaryHolderCkycNumber;
    private String secondHolderKycType;
    private String secondHolderCkycNumber;
    private String thirdHolderKycType;
    private String thirdHolderCkycNumber;
    private String guardianKycType;
    private String guardianCkycNumber;
    private String primaryHolderKraExemptRefNo;
    private String secondHolderKraExemptRefNo;
    private String thirdHolderKraExemptRefNo;
    private String guardianExemptRefNo;
    private String aadhaarUpdated;
    private String mapinId;
    private String paperlessFlag;
    private String leiNo;
    private String leiValidity;
    private String mobileDeclarationFlag;
    private String emailDeclarationFlag;
    private String secondHolderEmail;
    private String secondHolderEmailDeclarationFlag;
    private String secondHolderMobileNo;
    private String secondHolderMobileNoDeclarationFlag;
    private String thirdHolderEmail;
    private String thirdHolderEmailDeclarationFlag;
    private String thirdHolderMobileNo;
    private String thirdHolderMobileNoDeclarationFlag;
    private String guardianRelationship;
    private String nominationOpt;
    private String nominationAuthMode;
    private String nominee1Name;
    private String nominee1Relationship;
    private String nominee1Applicable;
    private String nominee1MinorFlag;
    private String nominee1Dob;
    private String nominee1Guardian;
    private String nominee1GuardianPan;
    private String nominee1IdType;
    private String nominee1IdNumber;
    private String nominee1Email;
    private String nominee1MobileNo;
    private String nominee1Address1;
    private String nominee1Address2;
    private String nominee1Address3;
    private String nominee1City;
    private String nominee1Pin;
    private String nominee1Country;
    private String nominee2Name;
    private String nominee2Relationship;
    private String nominee2Applicable;
    private String nominee2MinorFlag;
    private String nominee2Dob;
    private String nominee2Guardian;
    private String nominee2GuardianPan;
    private String nominee2IdType;
    private String nominee2IdNumber;
    private String nominee2Email;
    private String nominee2MobileNo;
    private String nominee2Address1;
    private String nominee2Address2;
    private String nominee2Address3;
    private String nominee2City;
    private String nominee2Pin;
    private String nominee2Country;
    private String nominee3Name;
    private String nominee3Relationship;
    private String nominee3Applicable;
    private String nominee3MinorFlag;
    private String nominee3Dob;
    private String nominee3Guardian;
    private String nominee3GuardianPan;
    private String nominee3IdType;
    private String nominee3IdNumber;
    private String nominee3Email;
    private String nominee3MobileNo;
    private String nominee3Address1;
    private String nominee3Address2;
    private String nominee3Address3;
    private String nominee3City;
    private String nominee3Pin;
    private String nominee3Country;
    private String filler1;
    private String filler2;
    private String filler3;
    private String filler4;
    private String filler5;
    private String filler6;
    private String filler7;
    private String filler8;
}
