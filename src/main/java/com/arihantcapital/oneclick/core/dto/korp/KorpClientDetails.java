package com.arihantcapital.oneclick.core.dto.korp;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;

@Data
public class KorpClientDetails {

    @JsonProperty("KYCDetail")
    private List<KycDetail> kycDetail;

    @JsonProperty("AddressDetail")
    private List<AddressDetail> addressDetail;

    @JsonProperty("BankDetail")
    private List<BankDetail> bankDetail;

    @JsonProperty("DepositoryDetail")
    private List<DepositoryDetail> depositoryDetail;

    @JsonProperty("DirectorDetail")
    private List<DirectorDetail> directorDetail;

    @JsonProperty("ContactDetail")
    private List<ContactDetail> contactDetail;

    @JsonProperty("NomineeDetail")
    private List<NomineeDetail> nomineeDetail;

    @JsonProperty("BrokerageMappingDetail")
    private List<BrokerageMappingDetail> brokerageMappingDetail;

    @JsonProperty("ExchangeDetail")
    private List<ExchangeDetail> exchangeDetail;

    @JsonProperty("SegmentDetail")
    private List<SegmentDetail> segmentDetail;

    @JsonProperty("ClientBackOfficeDetail")
    private List<ClientBackOfficeDetail> clientBackOfficeDetail;

    @JsonProperty("ClientDocumentDetail")
    private List<ClientDocumentDetail> clientDocumentDetail;

    @JsonProperty("OtherHolderDetail")
    private List<?> otherHolderDetail;

    @Data
    public static class KycDetail {
        @JsonProperty("FirmID")
        private String firmId;

        @JsonProperty("ClientCode")
        private String clientCode;

        @JsonProperty("ClientName")
        private String clientName;

        @JsonProperty("ClientPrefix")
        private String clientPrefix;

        @JsonProperty("PanNo")
        private String panNo;

        @JsonProperty("PanExempt")
        private String panExempt;

        @JsonProperty("ClientType")
        private String clientType;

        @JsonProperty("ClientStatus")
        private String clientStatus;

        @JsonProperty("FatherOrSpouse")
        private String fatherOrSpouse;

        @JsonProperty("FatherPrefix")
        private String fatherPrefix;

        @JsonProperty("FatherName")
        private String fatherName;

        @JsonProperty("MotherPrefix")
        private String motherPrefix;

        @JsonProperty("MotherName")
        private String motherName;

        @JsonProperty("Maidenprefix")
        private String maidenprefix;

        @JsonProperty("MaidenName")
        private String maidenName;

        @JsonProperty("DOB")
        private String dob;

        @JsonProperty("Gender")
        private String gender;

        @JsonProperty("MaritalStatus")
        private String maritalStatus;

        @JsonProperty("Citizenship")
        private String citizenship;

        @JsonProperty("ResidentialStatus")
        private String residentialStatus;

        @JsonProperty("Occupation")
        private String occupation;

        @JsonProperty("OccupationOther")
        private String occupationOther;

        @JsonProperty("IDProof")
        private String idproof;

        @JsonProperty("IDProofRef")
        private String idproofRef;

        @JsonProperty("IDProofExpiry")
        private String idproofExpiry;

        @JsonProperty("Aadhar")
        private String aadhar;

        @JsonProperty("IPVEmployee")
        private String ipvemployee;

        @JsonProperty("IPVDate")
        private String ipvdate;

        @JsonProperty("PEP")
        private String pep;

        @JsonProperty("DOC")
        private String doc;

        @JsonProperty("IncorporationPlace")
        private String incorporationPlace;

        @JsonProperty("CIN")
        private String cin;

        @JsonProperty("RegistrationNo")
        private String registrationNo;

        @JsonProperty("RegistrationDate")
        private String registrationDate;

        @JsonProperty("RegistrationPlace")
        private String registrationPlace;

        @JsonProperty("RegistrationAuthority")
        private String registrationAuthority;

        @JsonProperty("AnnualIncome")
        private String annualIncome;

        @JsonProperty("AnnualIncomeDate")
        private String annualIncomeDate;

        @JsonProperty("NetWorth")
        private String netWorth;

        @JsonProperty("NetWorthDate")
        private String netWorthDate;

        @JsonProperty("BranchID")
        private String branchId;

        @JsonProperty("SubBranchID")
        private String subBranchId;

        @JsonProperty("RMCode")
        private String rmcode;

        @JsonProperty("RMName")
        private String rmname;

        @JsonProperty("PayoutFlag")
        private String payoutFlag;

        @JsonProperty("SettlementDay")
        private String settlementDay;

        @JsonProperty("CKYCRefNo")
        private String ckycrefNo;

        @JsonProperty("KYCMode")
        private String kycmode;

        @JsonProperty("CKYCBatchNo")
        private String ckycbatchNo;

        @JsonProperty("CKYCExportDate")
        private String ckycexportDate;

        @JsonProperty("CKYCExportUser")
        private String ckycexportUser;

        @JsonProperty("KRAType")
        private String kratype;

        @JsonProperty("KYCBatchNo")
        private String kycbatchNo;

        @JsonProperty("KYCExportDate")
        private String kycexportDate;

        @JsonProperty("KYCExportBy")
        private String kycexportBy;

        @JsonProperty("SettlementDate")
        private String settlementDate;

        @JsonProperty("RiskCategory")
        private String riskCategory;

        @JsonProperty("RGESSFlag")
        private String rgessflag;

        @JsonProperty("TradingSoftwareType")
        private String tradingSoftwareType;

        @JsonProperty("CTCLExport")
        private String ctclexport;

        @JsonProperty("BUYBackPosting")
        private String buybackPosting;

        @JsonProperty("FamilyID")
        private String familyId;

        @JsonProperty("OpenDate")
        private String openDate;

        @JsonProperty("CloseDate")
        private String closeDate;

        @JsonProperty("CKYCCategory")
        private String ckyccategory;

        @JsonProperty("TypeOfDoc")
        private String typeOfDoc;

        @JsonProperty("HoldingType")
        private String holdingType;

        @JsonProperty("MFClientType")
        private String mfclientType;

        @JsonProperty("PMS")
        private String pms;

        @JsonProperty("DivPayMode")
        private String divPayMode;

        @JsonProperty("CommunicationMode")
        private String communicationMode;

        @JsonProperty("KYCType")
        private String kyctype;

        @JsonProperty("AadharUpdate")
        private String aadharUpdate;

        @JsonProperty("MapInID")
        private String mapInId;

        @JsonProperty("PaperLessFlag")
        private String paperLessFlag;

        @JsonProperty("LEINo")
        private String leino;

        @JsonProperty("LEIDate")
        private String leidate;

        @JsonProperty("AadharSeededFlag")
        private String aadharSeededFlag;

        @JsonProperty("MFCKYCRefNo")
        private String mfckycrefNo;

        @JsonProperty("LastModifyBy")
        private String lastModifyBy;

        @JsonProperty("LastModifyDate")
        private String lastModifyDate;

        @JsonProperty("ZoneID")
        private String zoneId;

        @JsonProperty("RegionID")
        private String regionId;
    }

    @Data
    public static class AddressDetail {
        @JsonProperty("AddressID")
        private String addressId;

        @JsonProperty("FirmID")
        private String firmId;

        @JsonProperty("ClientCode")
        private String clientCode;

        @JsonProperty("AddressType")
        private String addressType;

        @JsonProperty("AddressLine1")
        private String addressLine1;

        @JsonProperty("AddressLine2")
        private String addressLine2;

        @JsonProperty("AddressLine3")
        private String addressLine3;

        @JsonProperty("AddressCity")
        private String addressCity;

        @JsonProperty("AddressPinCode")
        private String addressPinCode;

        @JsonProperty("AddressState")
        private String addressState;

        @JsonProperty("AddressCountry")
        private String addressCountry;

        @JsonProperty("AddressPrimary")
        private String addressPrimary;

        @JsonProperty("AddressProof")
        private String addressProof;

        @JsonProperty("AddressProofOther")
        private String addressProofOther;

        @JsonProperty("AddressProofDate")
        private String addressProofDate;

        @JsonProperty("AddressProofExpiry")
        private String addressProofExpiry;

        @JsonProperty("AddressProofRef")
        private String addressProofRef;

        @JsonProperty("District")
        private String district;

        @JsonProperty("AddressStateOther")
        private String addressStateOther;

        @JsonProperty("AddressProofIssuedBy")
        private String addressProofIssuedBy;

        @JsonProperty("Delete")
        private String delete;
    }

    @Data
    public static class BankDetail {
        @JsonProperty("FirmID")
        private String firmId;

        @JsonProperty("BankID")
        private String bankId;

        @JsonProperty("ClientCode")
        private String clientCode;

        @JsonProperty("PrimaryFlag")
        private String primaryFlag;

        @JsonProperty("BankAccountNumber")
        private String bankAccountNumber;

        @JsonProperty("BankAccountType")
        private String bankAccountType;

        @JsonProperty("BankIFSC")
        private String bankIfsc;

        @JsonProperty("BankMICR")
        private String bankMicr;

        @JsonProperty("ChequePrintName")
        private String chequePrintName;

        @JsonProperty("BankCode")
        private String bankCode;

        @JsonProperty("BankName")
        private String bankName;

        @JsonProperty("BankAddress1")
        private String bankAddress1;

        @JsonProperty("BankAddress2")
        private String bankAddress2;

        @JsonProperty("BankAddress3")
        private String bankAddress3;

        @JsonProperty("BankCity")
        private String bankCity;

        @JsonProperty("BankPincode")
        private String bankPincode;

        @JsonProperty("BankState")
        private String bankState;

        @JsonProperty("BankSateOther")
        private String bankSateOther;

        @JsonProperty("BankCountry")
        private String bankCountry;

        @JsonProperty("POAFlag")
        private String poaflag;

        @JsonProperty("POADate")
        private String poadate;

        @JsonProperty("Delete")
        private String delete;

        @JsonProperty("CustomerID")
        private String customerId;

        @JsonProperty("UMRN")
        private String umrn;

        @JsonProperty("ECSMandateDate")
        private String ecsmandateDate;

        @JsonProperty("ECSFromDate")
        private String ecsfromDate;

        @JsonProperty("ECSToDate")
        private String ecstoDate;

        @JsonProperty("ECSUntilCancel")
        private String ecsuntilCancel;

        @JsonProperty("ECSStatus")
        private String ecsstatus;

        @JsonProperty("ECSFrequency")
        private String ecsfrequency;

        @JsonProperty("ECSLimit")
        private String ecslimit;

        @JsonProperty("AutoDebit")
        private String autoDebit;

        @JsonProperty("RejectionReason")
        private String rejectionReason;

        @JsonProperty("RBIApprovalNo")
        private String rbiapprovalNo;

        @JsonProperty("PISAccountNo")
        private String pisaccountNo;

        @JsonProperty("ACHCustomerID")
        private String achcustomerId;

        @JsonProperty("ActiveFlag")
        private String activeFlag;

        @JsonProperty("PennyID")
        private String pennyId;

        @JsonProperty("PennyVerify")
        private String pennyVerify;

        @JsonProperty("PennyErrorMessage")
        private String pennyErrorMessage;

        @JsonProperty("PennyDate")
        private String pennyDate;

        @JsonProperty("NameInBank")
        private String nameInBank;

        @JsonProperty("PennyMode")
        private String pennyMode;

        @JsonProperty("TMBankAccountType")
        private String tmbankAccountType;

        @JsonProperty("BankShortCode")
        private String bankShortCode;

        @JsonProperty("PGCode")
        private String pgcode;

        @JsonProperty("BankDomain")
        private String bankDomain;

        @JsonProperty("FundMandate")
        private String fundMandate;
    }

    @Data
    public static class DepositoryDetail {
        @JsonProperty("SerialNo")
        private String serialNo;

        @JsonProperty("FirmID")
        private String firmId;

        @JsonProperty("ClientCode")
        private String clientCode;

        @JsonProperty("DepositoryType")
        private String depositoryType;

        @JsonProperty("DepositoryID")
        private String depositoryId;

        @JsonProperty("DepositoryName")
        private String depositoryName;

        @JsonProperty("DepositoryClientID")
        private String depositoryClientId;

        @JsonProperty("POAFlag")
        private String poaflag;

        @JsonProperty("POAID")
        private String poaid;

        @JsonProperty("PrimaryFlag")
        private String primaryFlag;

        @JsonProperty("POAActiveDate")
        private String poaactiveDate;

        @JsonProperty("POAInactiveDate")
        private String poainactiveDate;

        @JsonProperty("POAMarginFlag")
        private String poamarginFlag;

        @JsonProperty("Delete")
        private String delete;

        @JsonProperty("Addendum")
        private String addendum;

        @JsonProperty("PledgeAllow")
        private String pledgeAllow;

        @JsonProperty("Holders")
        private String holders;

        @JsonProperty("DDPIFlag")
        private String ddpiflag;

        @JsonProperty("DDPIID")
        private String ddpiid;

        @JsonProperty("DDPIActiveDate")
        private String ddpiactiveDate;

        @JsonProperty("DDPIInactiveDate")
        private String ddpiinactiveDate;

        @JsonProperty("Status")
        private String status;

        @JsonProperty("DDPIORPOAFLAG")
        private String ddpiorpoaflag;

        @JsonProperty("TMAccountType")
        private String tmaccountType;
    }

    @Data
    public static class DirectorDetail {
        @JsonProperty("SlNo")
        private String slNo;

        @JsonProperty("FirmID")
        private String firmId;

        @JsonProperty("AccountID")
        private String accountId;

        @JsonProperty("PreFix")
        private String preFix;

        @JsonProperty("Name")
        private String name;

        @JsonProperty("Father")
        private String father;

        @JsonProperty("Designation")
        private String designation;

        @JsonProperty("DOB")
        private String dob;

        @JsonProperty("PANNO")
        private String panno;

        @JsonProperty("UID")
        private Long uid;

        @JsonProperty("DIN")
        private String din;

        @JsonProperty("Status")
        private String status;

        @JsonProperty("Email")
        private String email;

        @JsonProperty("ISD")
        private String isd;

        @JsonProperty("STD")
        private String std;

        @JsonProperty("Phone")
        private String phone;

        @JsonProperty("Mobile")
        private String mobile;

        @JsonProperty("Address1")
        private String address1;

        @JsonProperty("Address2")
        private String address2;

        @JsonProperty("Address3")
        private String address3;

        @JsonProperty("City")
        private String city;

        @JsonProperty("Pincode")
        private String pincode;

        @JsonProperty("District")
        private String district;

        @JsonProperty("State")
        private String state;

        @JsonProperty("StateOther")
        private String stateOther;

        @JsonProperty("Country")
        private String country;

        @JsonProperty("IDProof")
        private String idproof;

        @JsonProperty("IDProofOther")
        private String idproofOther;

        @JsonProperty("IDRefNo")
        private String idrefNo;

        @JsonProperty("IDRoofDate")
        private String idroofDate;

        @JsonProperty("IDPRoofExpiryDate")
        private String idproofExpiryDate;

        @JsonProperty("Delete")
        private String delete;

        @JsonProperty("FatherSpouse")
        private String fatherSpouse;

        @JsonProperty("FatherSpousePrefix")
        private String fatherSpousePrefix;

        @JsonProperty("Gender")
        private String gender;

        @JsonProperty("MotherPrefix")
        private String motherPrefix;

        @JsonProperty("MotherName")
        private String motherName;

        @JsonProperty("SerialNo")
        private String serialNo;

        @JsonProperty("AddressProof")
        private String addressProof;

        @JsonProperty("AddressProofNo")
        private String addressProofNo;

        @JsonProperty("Remark")
        private String remark;
    }

    @Data
    public static class ContactDetail {
        @JsonProperty("FirmID")
        private String firmId;

        @JsonProperty("ContactID")
        private String contactId;

        @JsonProperty("ClientCode")
        private String clientCode;

        @JsonProperty("ContactType")
        private String contactType;

        @JsonProperty("ISD")
        private String isd;

        @JsonProperty("STD")
        private String std;

        @JsonProperty("ContactNo")
        private String contactNo;

        @JsonProperty("ContactEmail")
        private String contactEmail;

        @JsonProperty("PrimaryFlag")
        private String primaryFlag;

        @JsonProperty("ActiveFlag")
        private String activeFlag;

        @JsonProperty("InactiveDate")
        private String inactiveDate;

        @JsonProperty("Remark")
        private String remark;

        @JsonProperty("Delete")
        private String delete;

        @JsonProperty("RelatedTo")
        private String relatedTo;

        @JsonProperty("MasterPAN")
        private String masterPan;
    }

    @Data
    public static class NomineeDetail {
        @JsonProperty("SLNO")
        private String slno;

        @JsonProperty("FirmID")
        private String firmId;

        @JsonProperty("ClientCode")
        private String clientCode;

        @JsonProperty("NomineeType")
        private String nomineeType;

        @JsonProperty("Relation")
        private String relation;

        @JsonProperty("PreFix")
        private String preFix;

        @JsonProperty("Name")
        private String name;

        @JsonProperty("FatherSpouse")
        private String fatherSpouse;

        @JsonProperty("FatherPreFix")
        private String fatherPreFix;

        @JsonProperty("FatherName")
        private String fatherName;

        @JsonProperty("MotherPreFix")
        private String motherPreFix;

        @JsonProperty("MotherName")
        private String motherName;

        @JsonProperty("MaidenPreFix")
        private String maidenPreFix;

        @JsonProperty("MaidenName")
        private String maidenName;

        @JsonProperty("DOB")
        private String dob;

        @JsonProperty("PANNO")
        private String panno;

        @JsonProperty("UID")
        private String uid;

        @JsonProperty("Gender")
        private String gender;

        @JsonProperty("MaritalStatus")
        private String maritalStatus;

        @JsonProperty("ResidentialStatus")
        private String residentialStatus;

        @JsonProperty("Nationnality")
        private String nationnality;

        @JsonProperty("Occupation")
        private String occupation;

        @JsonProperty("OccupationOther")
        private String occupationOther;

        @JsonProperty("IDProof")
        private String idproof;

        @JsonProperty("IDProofRefNo")
        private String idproofRefNo;

        @JsonProperty("Address1")
        private String address1;

        @JsonProperty("Address2")
        private String address2;

        @JsonProperty("Address3")
        private String address3;

        @JsonProperty("City")
        private String city;

        @JsonProperty("Pincode")
        private String pincode;

        @JsonProperty("District")
        private String district;

        @JsonProperty("State")
        private String state;

        @JsonProperty("StateOther")
        private String stateOther;

        @JsonProperty("Country")
        private String country;

        @JsonProperty("AddressProof")
        private String addressProof;

        @JsonProperty("AddressProofOther")
        private String addressProofOther;

        @JsonProperty("AddressProofRef")
        private String addressProofRef;

        @JsonProperty("AddressProofDate")
        private String addressProofDate;

        @JsonProperty("AddressProofDateExpiry")
        private String addressProofDateExpiry;

        @JsonProperty("Delete")
        private String delete;

        @JsonProperty("SharePercentage")
        private Double sharePercentage;

        @JsonProperty("EmailID")
        private String emailId;

        @JsonProperty("ISD")
        private String isd;

        @JsonProperty("Mobile")
        private String mobile;

        @JsonProperty("MinorInd")
        private String minorInd;

        @JsonProperty("NomineeSerialNo")
        private String nomineeSerialNo;

        @JsonProperty("NomineeStatusCheck")
        private String nomineeStatusCheck;
    }

    @Data
    public static class BrokerageMappingDetail {
        @JsonProperty("FirmID")
        private String firmId;

        @JsonProperty("ExchangeID")
        private String exchangeId;

        @JsonProperty("SegmentID")
        private String segmentId;

        @JsonProperty("SlabID")
        private String slabId;

        @JsonProperty("AccountID")
        private String accountId;

        @JsonProperty("StartDate")
        private String startDate;

        @JsonProperty("UpToDec")
        private Long upToDec;

        @JsonProperty("RoundTruncFlag")
        private String roundTruncFlag;

        @JsonProperty("OptionBrokMethod")
        private String optionBrokMethod;

        @JsonProperty("UpdateBy")
        private String updateBy;

        @JsonProperty("UpdateDate")
        private String updateDate;

        @JsonProperty("SellSlabID")
        private String sellSlabId;

        @JsonProperty("SecSlabType")
        private String secSlabType;
    }

    @Data
    public static class ExchangeDetail {
        @JsonProperty("FirmID")
        private String firmId;

        @JsonProperty("ClientCode")
        private String clientCode;

        @JsonProperty("ExchangeID")
        private String exchangeId;

        @JsonProperty("ExchangeName")
        private String exchangeName;

        @JsonProperty("CategoryCode")
        private String categoryCode;

        @JsonProperty("CategoryName")
        private String categoryName;

        @JsonProperty("UCC")
        private String ucc;

        @JsonProperty("ActiveFlag")
        private String activeFlag;
    }

    @Data
    public static class SegmentDetail {
        @JsonProperty("FirmID")
        private String firmId;

        @JsonProperty("ClientCode")
        private String clientCode;

        @JsonProperty("ExchangeID")
        private String exchangeId;

        @JsonProperty("ExchangeName")
        private String exchangeName;

        @JsonProperty("SegmentID")
        private String segmentId;

        @JsonProperty("SegmentName")
        private String segmentName;

        @JsonProperty("ActiveDate")
        private String activeDate;

        @JsonProperty("InactiveDate")
        private String inactiveDate;

        @JsonProperty("TradingAllow")
        private String tradingAllow;

        @JsonProperty("Remarks")
        private String remarks;

        @JsonProperty("CPCode")
        private String cpcode;

        @JsonProperty("CMID")
        private String cmid;

        @JsonProperty("UCCExported")
        private String uccexported;

        @JsonProperty("UCCExportDate")
        private String uccexportDate;

        @JsonProperty("UCCExportBy")
        private String uccexportBy;

        @JsonProperty("UCCSuccess")
        private String uccsuccess;

        @JsonProperty("UCCClassification")
        private String uccclassification;

        @JsonProperty("ExchangeVerification")
        private String exchangeVerification;

        @JsonProperty("CPCodePledge")
        private String cpcodePledge;

        @JsonProperty("LastTradeDate")
        private String lastTradeDate;

        @JsonProperty("PermitToTrade")
        private String permitToTrade;

        @JsonProperty("TradingBlock")
        private String tradingBlock;

        @JsonProperty("TradingBlockDate")
        private String tradingBlockDate;

        @JsonProperty("TradingBlockBy")
        private String tradingBlockBy;

        @JsonProperty("ExcludeSegregation")
        private String excludeSegregation;
    }

    @Data
    public static class ClientBackOfficeDetail {
        @JsonProperty("FirmID")
        private String firmId;

        @JsonProperty("AccountID")
        private String accountId;

        @JsonProperty("ProtectedAccount")
        private String protectedAccount;

        @JsonProperty("ModifyRemark")
        private String modifyRemark;

        @JsonProperty("UIDEnrollmentNo")
        private String uidenrollmentNo;

        @JsonProperty("PayoutFlag")
        private String payoutFlag;

        @JsonProperty("SettlementDay")
        private String settlementDay;

        @JsonProperty("CKYCRefNo")
        private String ckycrefNo;

        @JsonProperty("KYCMode")
        private String kycmode;

        @JsonProperty("CKYCBatchNo")
        private String ckycbatchNo;

        @JsonProperty("CKYCExportDate")
        private String ckycexportDate;

        @JsonProperty("CKYCExportUser")
        private String ckycexportUser;

        @JsonProperty("KRAType")
        private String kratype;

        @JsonProperty("KYCBatchNo")
        private String kycbatchNo;

        @JsonProperty("KYCExportDate")
        private String kycexportDate;

        @JsonProperty("KYCExportBy")
        private String kycexportBy;

        @JsonProperty("SettlementDate")
        private String settlementDate;

        @JsonProperty("RiskCategory")
        private String riskCategory;

        @JsonProperty("RGESSFlag")
        private String rgessflag;

        @JsonProperty("TradingSoftwareType")
        private String tradingSoftwareType;

        @JsonProperty("CTCLExport")
        private String ctclexport;

        @JsonProperty("BuyBackPosting")
        private String buyBackPosting;

        @JsonProperty("KYCReferenceNo")
        private String kycreferenceNo;

        @JsonProperty("PaymentType")
        private String paymentType;

        @JsonProperty("CTCLCategory")
        private String ctclcategory;

        @JsonProperty("GSTRegistrationNo")
        private String gstregistrationNo;

        @JsonProperty("GSTRegistrationDate")
        private String gstregistrationDate;

        @JsonProperty("AadharVirtualID")
        private String aadharVirtualId;

        @JsonProperty("UPIID")
        private String upiid;

        @JsonProperty("FATCAFlag")
        private String fatcaflag;

        @JsonProperty("MinorFlag")
        private String minorFlag;

        @JsonProperty("MTFStatus")
        private String mtfstatus;

        @JsonProperty("MTFStatusDate")
        private String mtfstatusDate;

        @JsonProperty("MTFRemarks")
        private String mtfremarks;

        @JsonProperty("ModifiedBy")
        private String modifiedBy;

        @JsonProperty("ModifiedDate")
        private String modifiedDate;

        @JsonProperty("DormateRemark")
        private String dormateRemark;

        @JsonProperty("DormateUpdateBy")
        private String dormateUpdateBy;

        @JsonProperty("DormateUpdateDate")
        private String dormateUpdateDate;

        @JsonProperty("ClientShare")
        private String clientShare;

        @JsonProperty("Status")
        private String status;

        @JsonProperty("StatusDesc")
        private String statusDesc;

        @JsonProperty("CSC")
        private String csc;

        @JsonProperty("APPANNo")
        private String appanno;

        @JsonProperty("NomineeOptFlag")
        private String nomineeOptFlag;

        @JsonProperty("UnTracedFlag")
        private String unTracedFlag;

        @JsonProperty("EducationCode")
        private String educationCode;

        @JsonProperty("OtherMktExperience")
        private String otherMktExperience;

        @JsonProperty("StockMktExperience")
        private String stockMktExperience;

        @JsonProperty("DerivativeMktExperience")
        private String derivativeMktExperience;

        @JsonProperty("LanguageCode")
        private String languageCode;

        @JsonProperty("FATCACountry")
        private String fatcacountry;

        @JsonProperty("FATCATin")
        private String fatcatin;

        @JsonProperty("FATCADate")
        private String fatcadate;

        @JsonProperty("LastTradeDate")
        private String lastTradeDate;

        @JsonProperty("CVLKRAStatus")
        private String cvlkrastatus;

        @JsonProperty("UPIOptFlag")
        private String upioptFlag;

        @JsonProperty("POAExempt")
        private String poaexempt;

        @JsonProperty("HoldPayoutAmount")
        private String holdPayoutAmount;

        @JsonProperty("OnlineTrading")
        private String onlineTrading;

        @JsonProperty("KYCModifBatchFlag")
        private String kycmodifBatchFlag;

        @JsonProperty("FATCABirthPlace")
        private String fatcabirthPlace;

        @JsonProperty("FATCACountryOfBirth")
        private String fatcacountryOfBirth;

        @JsonProperty("FATCATINExempt")
        private String fatcatinexempt;

        @JsonProperty("FATCATINExemptReason")
        private String fatcatinexemptReason;

        @JsonProperty("AnnualIncomeAmount")
        private String annualIncomeAmount;

        @JsonProperty("MTFFundingLimit")
        private String mtffundingLimit;

        @JsonProperty("LUTNo")
        private String lutno;

        @JsonProperty("SEZFlag")
        private String sezflag;

        @JsonProperty("NPOFlag")
        private String npoflag;

        @JsonProperty("DARPANID")
        private String darpanid;

        @JsonProperty("DARPANDT")
        private String darpandt;

        @JsonProperty("DPStatementFlag")
        private String dpstatementFlag;

        @JsonProperty("FormNo")
        private String formNo;

        @JsonProperty("ClientCategory")
        private String clientCategory;
    }

    @Data
    public static class ClientDocumentDetail {
        @JsonProperty("FirmID")
        private String firmId;

        @JsonProperty("AccountID")
        private String accountId;

        @JsonProperty("DocumentID")
        private String documentId;

        @JsonProperty("DocumentName")
        private String documentName;

        @JsonProperty("DocumentFileName")
        private String documentFileName;

        @JsonProperty("Remarks")
        private String remarks;

        @JsonProperty("DataAvilable")
        private String dataAvilable;

        @JsonProperty("DocData")
        private String docData;
    }
}
