package com.arihantcapital.oneclick.core.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;

@Data
public class BseUccClientV5 {
    @JsonProperty("TRANSACTIONCODE")
    private String transactioncode;

    @JsonProperty("CLIENTTYPE")
    private String clienttype;

    @JsonProperty("STATUS")
    private String status;

    @JsonProperty("CATEGORY")
    private String category;

    @JsonProperty("CLIENTCODE")
    private String clientcode;

    @JsonProperty("PANNO")
    private String panno;

    @JsonProperty("POLITICALEXPERSON")
    private String politicalexperson;

    @JsonProperty("ADDRESS1")
    private String address1;

    @JsonProperty("PERMNEQUALCORP")
    private String permnequalcorp;

    @JsonProperty("ADDRESS2")
    private String address2;

    @JsonProperty("COUNTRY")
    private String country;

    @JsonProperty("STATE")
    private String state;

    @JsonProperty("CITY")
    private String city;

    @JsonProperty("PINCODE")
    private String pincode;

    @JsonProperty("TYPEOFSERVICE")
    private String typeofservice;

    @JsonProperty("CONTACTDETAILS")
    private String contactdetails;

    @JsonProperty("EMAIL")
    private String email;

    @JsonProperty("MOBILENUMBER")
    private String mobilenumber;

    @JsonProperty("STDCODE")
    private String stdcode;

    @JsonProperty("PHONENO")
    private String phoneno;

    @JsonProperty("EQ_CPCODE")
    private String eqCpcode;

    @JsonProperty("EQCMID")
    private String eqcmid;

    @JsonProperty("FNOCPCODE")
    private String fnocpcode;

    @JsonProperty("FNOCMID")
    private String fnocmid;

    @JsonProperty("DEPOSITORYNAME1")
    private String depositoryname1;

    @JsonProperty("DEMANTID1")
    private String demantid1;

    @JsonProperty("DEPOSITORYPARTICIPANT1")
    private String depositoryparticipant1;

    @JsonProperty("DEPOSITORYNAME2")
    private String depositoryname2;

    @JsonProperty("DEMANTID2")
    private String demantid2;

    @JsonProperty("DEPOSITORYPARTICIPANT2")
    private String depositoryparticipant2;

    @JsonProperty("DEPOSITORYNAME3")
    private String depositoryname3;

    @JsonProperty("DEMANTID3")
    private String demantid3;

    @JsonProperty("DEPOSITORYPARTICIPANT3")
    private String depositoryparticipant3;

    @JsonProperty("BANKNAME1")
    private String bankname1;

    @JsonProperty("ACCOUNTNO1")
    private String accountno1;

    @JsonProperty("BANKNAME2")
    private String bankname2;

    @JsonProperty("ACCOUNTNO2")
    private String accountno2;

    @JsonProperty("BANKNAME3")
    private String bankname3;

    @JsonProperty("ACCOUNTNO3")
    private String accountno3;

    @JsonProperty("CLIENTAGGREMENTDATE")
    private String clientaggrementdate;

    @JsonProperty("PROVIDEDETAILS")
    private String providedetails;

    @JsonProperty("INCOME")
    private String income;

    @JsonProperty("INCOMEDATE")
    private String incomedate;

    @JsonProperty("NETWORTH")
    private String networth;

    @JsonProperty("NETWORTHDATE")
    private String networthdate;

    @JsonProperty("ISACTIVE")
    private String isactive;

    @JsonProperty("UPDATEREASON")
    private String updatereason;

    @JsonProperty("FIRSTNAME")
    private String firstname;

    @JsonProperty("MIDDLENAME")
    private String middlename;

    @JsonProperty("LASTNAME")
    private String lastname;

    @JsonProperty("AADHARCARDNO")
    private String aadharcardno;

    @JsonProperty("DATEOFBIRTH")
    private String dateofbirth;

    @JsonProperty("CLIENTNAME")
    private String clientname;

    @JsonProperty("REGISTRATIONNO")
    private String registrationno;

    @JsonProperty("REGISTERINGAUTHORITY")
    private String registeringauthority;

    @JsonProperty("DATEOFREGISTRATION")
    private String dateofregistration;

    @JsonProperty("PLACEOFREGISTRATION")
    private String placeofregistration;

    @JsonProperty("WHETHERCORPORATE")
    private String whethercorporate;

    @JsonProperty("CINNO")
    private String cinno;

    @JsonProperty("NUMBEROFDIRECTORS")
    private String numberofdirectors;

    @JsonProperty("PARTNERS_KARTAUID")
    private String partnersKartauid;

    @JsonProperty("PARTNERS_COPARCENERUID")
    private String partnersCoparceneruid;

    @JsonProperty("CONTACTPERSONNAME1")
    private String contactpersonname1;

    @JsonProperty("CONTACTPERSONDESIGNATION1")
    private String contactpersondesignation1;

    @JsonProperty("CONTACTPERSONADDRESS1")
    private String contactpersonaddress1;

    @JsonProperty("CONTACTPERSONEMAIL1")
    private String contactpersonemail1;

    @JsonProperty("CONTACTPERSONPAN1")
    private String contactpersonpan1;

    @JsonProperty("CONTACTPERSONMOBILE1")
    private String contactpersonmobile1;

    @JsonProperty("CONTACTPERSONNAME2")
    private String contactpersonname2;

    @JsonProperty("CONTACTPERSONDESIGNATION2")
    private String contactpersondesignation2;

    @JsonProperty("CONTACTPERSONADDRESS2")
    private String contactpersonaddress2;

    @JsonProperty("CONTACTPERSONEMAIL2")
    private String contactpersonemail2;

    @JsonProperty("CONTACTPERSONPAN2")
    private String contactpersonpan2;

    @JsonProperty("CONTACTPERSONMOBILE2")
    private String contactpersonmobile2;

    @JsonProperty("SERVERIP")
    private String serverip;

    @JsonProperty("BATUSER")
    private String batuser;

    @JsonProperty("CASH")
    private String cash;

    @JsonProperty("EQUITY_DERIVATIVE")
    private String equityDerivative;

    @JsonProperty("SLB")
    private String slb;

    @JsonProperty("CURRENCY")
    private String currency;

    @JsonProperty("DEBT")
    private String debt;

    @JsonProperty("ISPOA")
    private String ispoa;

    @JsonProperty("POAFORFUND")
    private String poaforfund;

    @JsonProperty("POAFORSECURITY")
    private String poaforsecurity;

    @JsonProperty("DATEOFPOAFORFUND")
    private String dateofpoaforfund;

    @JsonProperty("DATEOFPOAFORSECURITY")
    private String dateofpoaforsecurity;

    @JsonProperty("PERCOUNTRY")
    private String percountry;

    @JsonProperty("PERSTATE")
    private String perstate;

    @JsonProperty("PERCITY")
    private String percity;

    @JsonProperty("PERPINCODE")
    private String perpincode;

    @JsonProperty("CURRENCYCPCODE")
    private String currencycpcode;

    @JsonProperty("CURRENCYCMID")
    private String currencycmid;

    @JsonProperty("ENROLLMENTNUMBER")
    private String enrollmentnumber;

    @JsonProperty("COMMDERIVATIVES")
    private String commderivatives;

    @JsonProperty("OPTEDFORNOMINATION")
    private String optedfornomination;

    @JsonProperty("CUSTPANNO")
    private String custpanno;

    @JsonProperty("CUSTNAME")
    private String custname;

    @JsonProperty("CUSTADDRESS")
    private String custaddress;

    @JsonProperty("CUSTCOUNTRY")
    private String custcountry;

    @JsonProperty("CUSTSTATE")
    private String custstate;

    @JsonProperty("CUSTCITY")
    private String custcity;

    @JsonProperty("CUSTPINCODE")
    private String custpincode;

    @JsonProperty("CUSTMOBILENUMBER")
    private String custmobilenumber;

    @JsonProperty("CUSTSTDCODE")
    private String custstdcode;

    @JsonProperty("CUSTPHONENO")
    private String custphoneno;

    @JsonProperty("CUSTEMAIL")
    private String custemail;

    @JsonProperty("EGR")
    private String egr;

    @JsonProperty("BENEFICIALOWNACNTNO1")
    private String beneficialownacntno1;

    @JsonProperty("BENEFICIALOWNACNTNO2")
    private String beneficialownacntno2;

    @JsonProperty("BENEFICIALOWNACNTNO3")
    private String beneficialownacntno3;

    @JsonProperty("OPTED_FOR_UPI")
    private String optedForUpi;

    @JsonProperty("BANKBRANCHIFSCCODE1")
    private String bankbranchifsccode1;

    @JsonProperty("PRIMARYORSECONDARYBANK1")
    private String primaryorsecondarybank1;

    @JsonProperty("BANKBRANCHIFSCCODE2")
    private String bankbranchifsccode2;

    @JsonProperty("PRIMARYORSECONDARYBANK2")
    private String primaryorsecondarybank2;

    @JsonProperty("BANKBRANCHIFSCCODE3")
    private String bankbranchifsccode3;

    @JsonProperty("PRIMARYORSECONDARYBANK3")
    private String primaryorsecondarybank3;

    @JsonProperty("BANKBRANCHIFSCCODE4")
    private String bankbranchifsccode4;

    @JsonProperty("BANKNAME4")
    private String bankname4;

    @JsonProperty("ACCOUNTNO4")
    private String accountno4;

    @JsonProperty("PRIMARYORSECONDARYBANK4")
    private String primaryorsecondarybank4;

    @JsonProperty("BANKBRANCHIFSCCODE5")
    private String bankbranchifsccode5;

    @JsonProperty("BANKNAME5")
    private String bankname5;

    @JsonProperty("ACCOUNTNO5")
    private String accountno5;

    @JsonProperty("PRIMARYORSECONDARYBANK5")
    private String primaryorsecondarybank5;

    @JsonProperty("PRIMARYORSECONDARYDP1")
    private String primaryorsecondarydp1;

    @JsonProperty("PRIMARYORSECONDARYDP2")
    private String primaryorsecondarydp2;

    @JsonProperty("PRIMARYORSECONDARYDP3")
    private String primaryorsecondarydp3;

    @JsonProperty("DEPOSITORYNAME4")
    private String depositoryname4;

    @JsonProperty("DEMANTID4")
    private String demantid4;

    @JsonProperty("BENEFICIALOWNACNTNO4")
    private String beneficialownacntno4;

    @JsonProperty("PRIMARYORSECONDARYDP4")
    private String primaryorsecondarydp4;

    @JsonProperty("DEPOSITORYNAME5")
    private String depositoryname5;

    @JsonProperty("DEMANTID5")
    private String demantid5;

    @JsonProperty("BENEFICIALOWNACNTNO5")
    private String beneficialownacntno5;

    @JsonProperty("PRIMARYORSECONDARYDP5")
    private String primaryorsecondarydp5;

    @JsonProperty("CLIENTNAMEDESCRIPTION")
    private String clientnamedescription;

    @JsonProperty("DIRECTORDETAILS")
    private List<DirectorDetails> directordetails;

    @Data
    public static class DirectorDetails {
        @JsonProperty("OPERATIONFLAG")
        private String operationflag;

        @JsonProperty("CLIENTCODE")
        private String clientcode;

        @JsonProperty("NAMEOFDIRECTOR")
        private String nameofdirector;

        @JsonProperty("DIN")
        private String din;

        @JsonProperty("WHETHERFOREIGNRESIDENT")
        private String whetherforeignresident;

        @JsonProperty("PANNO")
        private String panno;

        @JsonProperty("DIRDESIGNATION")
        private String dirdesignation;

        @JsonProperty("DIRALLOWTOTRADE")
        private String dirallowtotrade;

        @JsonProperty("DIRADDRESS")
        private String diraddress;

        @JsonProperty("DIREMAILID")
        private String diremailid;

        @JsonProperty("DIRAADHARCARDNO")
        private String diraadharcardno;

        @JsonProperty("DIRENROLLMENTNO")
        private String direnrollmentno;

        @JsonProperty("DIRMOBILENO")
        private String dirmobileno;
    }
}
