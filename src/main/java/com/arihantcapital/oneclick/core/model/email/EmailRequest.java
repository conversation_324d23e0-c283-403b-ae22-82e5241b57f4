package com.arihantcapital.oneclick.core.model.email;

import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmailRequest {
    private String fromAddress;
    private String fromName;
    private List<String> recipients;
    private List<String> cc;
    private List<String> bcc;
    private String subject;
    private String text;
    private String html;
    private List<EmailAttachment> attachments;
    private Map<String, Object> templateVariables;
    private String templateName;
    private EmailPriority priority = EmailPriority.NORMAL;
    private boolean trackDelivery = false;

    public enum EmailPriority {
        LOW,
        NORMAL,
        HIGH,
        URGENT
    }
}
