package com.arihantcapital.oneclick.core.model.email;

import jakarta.activation.DataSource;
import jakarta.mail.util.ByteArrayDataSource;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class EmailAttachment {
    private String filename;
    private DataSource dataSource;
    private String contentType;
    private byte[] content;

    public DataSource getDataSource() {
        if (dataSource != null) {
            return dataSource;
        }
        return new ByteArrayDataSource(content, contentType);
    }
}
