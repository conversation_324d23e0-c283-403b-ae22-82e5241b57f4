package com.arihantcapital.oneclick.core.controller;

import com.arihantcapital.oneclick.core.service.BseUccService;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api/bse-ucc")
@RequiredArgsConstructor
public class BseUccController {

    private final BseUccService bseUccService;

    @PostMapping(value = "/add-ucc-data")
    public ResponseEntity<String> addUccData() {
        return ResponseEntity.ok(bseUccService.addUccData());
    }

    @PostMapping(value = "/reset-password")
    public ResponseEntity<String> resetPassword() {
        return ResponseEntity.ok(bseUccService.resetPassword());
    }

    @PostMapping(value = "/get-pan-status")
    public ResponseEntity<String> getPanStatus(@RequestBody JsonNode payload) {
        return ResponseEntity.ok(bseUccService.getPanStatus(payload));
    }

    @PostMapping(value = "/add-custodian-data")
    public ResponseEntity<String> addCustodianData() {
        return ResponseEntity.ok(bseUccService.addCustodianData());
    }
}
