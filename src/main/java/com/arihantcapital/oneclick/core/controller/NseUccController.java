package com.arihantcapital.oneclick.core.controller;

import com.arihantcapital.oneclick.core.service.NseUccService;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = "/api/nse-ucc")
@RequiredArgsConstructor
public class NseUccController {
    private final NseUccService nseUccService;

    @GetMapping(value = "/authenticate")
    public ResponseEntity<String> authenticate() {
        return ResponseEntity.ok(nseUccService.authenticate());
    }

    @PostMapping(value = "/client-upload")
    public ResponseEntity<String> clientUpload(@RequestBody JsonNode payload) {
        return ResponseEntity.ok(nseUccService.clientUpload(payload));
    }

    @PostMapping(value = "/get-pan-status")
    public ResponseEntity<String> getPanStatus(@RequestBody JsonNode payload) {
        return ResponseEntity.ok(nseUccService.getPanStatus(payload));
    }

    @PostMapping(value = "/get-ucc-pan-status")
    public ResponseEntity<String> getUccPanStatus(@RequestBody JsonNode payload) {
        return ResponseEntity.ok(nseUccService.getUccPanStatus(payload));
    }
}
