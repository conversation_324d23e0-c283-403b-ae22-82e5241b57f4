package com.arihantcapital.oneclick.core.controller;

import com.arihantcapital.oneclick.core.dto.ckyc.CkycVerifyRQ;
import com.arihantcapital.oneclick.core.service.CkycVerificationService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api/ckyc")
@RequiredArgsConstructor
public class CkycVerificationController {

    private final CkycVerificationService ckycVerificationService;

    @PostMapping(value = "/verify")
    public ResponseEntity<String> ckycVerify(@Valid @RequestBody CkycVerifyRQ ckycVerifyRQ) {
        return ResponseEntity.ok(ckycVerificationService.ckycVerify(ckycVerifyRQ));
    }
}
