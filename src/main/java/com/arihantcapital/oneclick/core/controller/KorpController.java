package com.arihantcapital.oneclick.core.controller;

import com.arihantcapital.oneclick.core.service.KorpService;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api/korp")
@RequiredArgsConstructor
public class KorpController {

    private final KorpService korpService;

    @GetMapping(value = "/authenticate")
    public ResponseEntity<String> authenticate() {
        return ResponseEntity.ok(korpService.authenticate());
    }

    @GetMapping(value = "/get-client-master")
    public ResponseEntity<JsonNode> getClientMaster(
            @RequestParam(value = "clientCode", required = true) String clientCode) {
        return ResponseEntity.ok(korpService.getClientMaster(clientCode));
    }

    @GetMapping(value = "/get-branch-master")
    public ResponseEntity<JsonNode> getBranchMaster(
            @RequestParam(value = "branchCode", required = false) String branchCode) {
        return ResponseEntity.ok(korpService.getBranchMaster(branchCode));
    }
}
