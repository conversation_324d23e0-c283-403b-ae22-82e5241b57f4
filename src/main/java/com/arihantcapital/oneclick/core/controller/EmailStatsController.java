package com.arihantcapital.oneclick.core.controller;

import com.arihantcapital.oneclick.core.email.EmailDeliveryLogService;
import com.arihantcapital.oneclick.core.email.EmailHealthCheckService;
import java.util.Map;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api/email")
public class EmailStatsController {
    private final EmailDeliveryLogService logService;
    private final EmailHealthCheckService healthCheckService;

    public EmailStatsController(EmailDeliveryLogService logService, EmailHealthCheckService healthCheckService) {
        this.logService = logService;
        this.healthCheckService = healthCheckService;
    }

    @GetMapping("/health")
    public ResponseEntity<Map<String, Boolean>> getProviderHealth() {
        return ResponseEntity.ok(healthCheckService.getHealthStatus());
    }

    //    @GetMapping("/stats")
    //    public ResponseEntity<EmailStats> getEmailStats() {
    //        // Implementation depends on your statistics requirements
    //        return ResponseEntity.ok(new EmailStats());
    //    }

}
