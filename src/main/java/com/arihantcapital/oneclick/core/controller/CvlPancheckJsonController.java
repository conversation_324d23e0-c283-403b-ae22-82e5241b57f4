package com.arihantcapital.oneclick.core.controller;

import com.arihantcapital.oneclick.core.dto.kra.cvl.pancheck.GetKraRQ;
import com.arihantcapital.oneclick.core.dto.kra.cvl.pancheck.GetPanStatusRQ;
import com.arihantcapital.oneclick.core.service.CvlPancheckJsonService;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = "/api/kra/cvl/pancheck/json")
@RequiredArgsConstructor
public class CvlPancheckJsonController {

    private final CvlPancheckJsonService cvlPancheckJsonService;

    @GetMapping(value = "/get-token")
    public ResponseEntity<JsonNode> jsonGetToken(
            @RequestParam(value = "tokenValidTime", required = false) String tokenValidTime) {
        return ResponseEntity.ok(cvlPancheckJsonService.getToken(tokenValidTime));
    }

    @PostMapping(value = "/get-pan-status")
    public ResponseEntity<JsonNode> jsonGetPanStatus(@Valid @RequestBody GetPanStatusRQ getPanStatusRQ) {
        return ResponseEntity.ok(cvlPancheckJsonService.getPanStatus(getPanStatusRQ.getPanNumber()));
    }

    @PostMapping(value = "/solicit-pan-details-fetch-all-kra")
    public ResponseEntity<JsonNode> jsonSolicitPANDetailsFetchALLKRA(@Valid @RequestBody GetKraRQ getKraRQ) {
        return ResponseEntity.ok(cvlPancheckJsonService.solicitPANDetailsFetchALLKRA(
                getKraRQ.getPanNumber(), getKraRQ.getKraCode(), getKraRQ.getDob(), getKraRQ.getFetchType()));
    }

    @PostMapping(value = "/insert-update-kyc-record")
    public ResponseEntity<JsonNode> jsonInsertUpdateKYCRecord() {
        return ResponseEntity.ok(cvlPancheckJsonService.insertUpdateKYCRecord(null));
    }
}
