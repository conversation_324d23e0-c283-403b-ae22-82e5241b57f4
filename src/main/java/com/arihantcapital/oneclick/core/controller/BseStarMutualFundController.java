package com.arihantcapital.oneclick.core.controller;

import com.arihantcapital.oneclick.core.service.BseStarMutualFundService;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api/bse-star-mutual-fund")
@RequiredArgsConstructor
public class BseStarMutualFundController {

    private final BseStarMutualFundService bseStarMutualFundService;

    @PostMapping(value = "/registration")
    public ResponseEntity<String> registration(@RequestBody JsonNode payload) {
        return ResponseEntity.ok(bseStarMutualFundService.registration(
                payload.get("regnType").asText(), payload.get("param").asText()));
    }

    @PostMapping(value = "/registration-v183")
    public ResponseEntity<String> registrationV183(@RequestBody JsonNode payload) {
        return ResponseEntity.ok(bseStarMutualFundService.registrationV183(
                payload.get("regnType").asText(), payload.get("param").asText()));
    }

    @PostMapping(value = "/nominee-registration-v56")
    public ResponseEntity<String> nomineeRegistrationV56(@RequestBody JsonNode payload) {
        return ResponseEntity.ok(bseStarMutualFundService.nomineeRegistrationV56(
                payload.get("regnType").asText(), payload.get("param").asText()));
    }
}
