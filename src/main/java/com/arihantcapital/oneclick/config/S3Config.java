package com.arihantcapital.oneclick.config;

import com.arihantcapital.oneclick.OneclickProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.AwsCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;

@Configuration
public class S3Config {

    @Autowired
    private OneclickProperties oneclickProperties;

    @Bean
    public S3Client s3Client() {
        AwsCredentials credentials = AwsBasicCredentials.create(
                oneclickProperties.s3().accessKey(), oneclickProperties.s3().secretKey());

        return S3Client.builder()
                .region(Region.of(oneclickProperties.s3().region()))
                .credentialsProvider(StaticCredentialsProvider.create(credentials))
                .build();
    }

    @Bean
    public S3Presigner s3Presigner() {
        AwsCredentials credentials = AwsBasicCredentials.create(
                oneclickProperties.s3().accessKey(), oneclickProperties.s3().secretKey());

        return S3Presigner.builder()
                .region(Region.of(oneclickProperties.s3().region()))
                .credentialsProvider(StaticCredentialsProvider.create(credentials))
                .build();
    }
}
