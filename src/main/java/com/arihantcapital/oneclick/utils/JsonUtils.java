package com.arihantcapital.oneclick.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.TextNode;
import java.util.Iterator;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
public class JsonUtils {

    private final ObjectMapper objectMapper;

    public JsonUtils() {
        this.objectMapper = new ObjectMapper();
    }

    public JsonUtils(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    /**
     * Recursively processes all string fields in a JsonNode - trims and converts to uppercase
     *
     * @param node The JsonNode to process
     * @return A new JsonNode with processed string values
     */
    public JsonNode processStringFields(JsonNode node) {
        if (node == null) {
            return null;
        }

        return processNode(node);
    }

    private JsonNode processNode(JsonNode node) {
        if (node.isTextual()) {
            // Process string values - trim and uppercase
            String processedValue = node.asText().trim().toUpperCase();
            return new TextNode(processedValue);
        } else if (node.isObject()) {
            return processObjectNode((ObjectNode) node);
        } else if (node.isArray()) {
            return processArrayNode((ArrayNode) node);
        } else {
            // For other types (numbers, booleans, null), return as-is
            return node.deepCopy();
        }
    }

    private ObjectNode processObjectNode(ObjectNode objectNode) {
        ObjectNode processedObject = objectMapper.createObjectNode();

        Iterator<Map.Entry<String, JsonNode>> fields = objectNode.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> field = fields.next();
            String fieldName = field.getKey();
            JsonNode fieldValue = field.getValue();

            // Recursively process the field value
            JsonNode processedValue = processNode(fieldValue);
            processedObject.set(fieldName, processedValue);
        }

        return processedObject;
    }

    private ArrayNode processArrayNode(ArrayNode arrayNode) {
        ArrayNode processedArray = objectMapper.createArrayNode();

        for (JsonNode element : arrayNode) {
            // Recursively process each array element
            JsonNode processedElement = processNode(element);
            processedArray.add(processedElement);
        }

        return processedArray;
    }

    /**
     * Convenience method for processing with custom string transformation
     *
     * @param node        The JsonNode to process
     * @param transformer Custom string transformer function
     * @return A new JsonNode with processed string values
     */
    public JsonNode processStringFields(JsonNode node, StringTransformer transformer) {
        return processNodeWithTransformer(node, transformer);
    }

    private JsonNode processNodeWithTransformer(JsonNode node, StringTransformer transformer) {
        if (node == null) {
            return null;
        }

        if (node.isTextual()) {
            String processedValue = transformer.transform(node.asText());
            return new TextNode(processedValue);
        } else if (node.isObject()) {
            return processObjectNodeWithTransformer((ObjectNode) node, transformer);
        } else if (node.isArray()) {
            return processArrayNodeWithTransformer((ArrayNode) node, transformer);
        } else {
            return node.deepCopy();
        }
    }

    private ObjectNode processObjectNodeWithTransformer(ObjectNode objectNode, StringTransformer transformer) {
        ObjectNode processedObject = objectMapper.createObjectNode();

        Iterator<Map.Entry<String, JsonNode>> fields = objectNode.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> field = fields.next();
            String fieldName = field.getKey();
            JsonNode fieldValue = field.getValue();

            JsonNode processedValue = processNodeWithTransformer(fieldValue, transformer);
            processedObject.set(fieldName, processedValue);
        }

        return processedObject;
    }

    private ArrayNode processArrayNodeWithTransformer(ArrayNode arrayNode, StringTransformer transformer) {
        ArrayNode processedArray = objectMapper.createArrayNode();

        for (JsonNode element : arrayNode) {
            JsonNode processedElement = processNodeWithTransformer(element, transformer);
            processedArray.add(processedElement);
        }

        return processedArray;
    }

    @FunctionalInterface
    public interface StringTransformer {
        String transform(String input);
    }

    // Usage example
    public static void main(String[] args) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonUtils processor = new JsonUtils(mapper);

            // Example JSON with nested structure
            String jsonString =
                    """
                    {
                        "name": "  john doe  ",
                        "email": " <EMAIL> ",
                        "address": {
                            "street": "  123 main st  ",
                            "city": " new york ",
                            "coordinates": [40.7128, -74.0060]
                        },
                        "hobbies": [
                            "  reading  ",
                            "  swimming  ",
                            {
                                "type": "  outdoor  ",
                                "activities": ["  hiking  ", "  camping  "]
                            }
                        ],
                        "age": 30,
                        "active": true
                    }
                    """;

            JsonNode originalNode = mapper.readTree(jsonString);
            System.out.println("Original:");
            System.out.println(mapper.writerWithDefaultPrettyPrinter().writeValueAsString(originalNode));

            // Process with default trim + uppercase
            JsonNode processedNode = processor.processStringFields(originalNode);
            System.out.println("\nProcessed (Trim + Uppercase):");
            System.out.println(mapper.writerWithDefaultPrettyPrinter().writeValueAsString(processedNode));

            // Process with custom transformer (trim + lowercase)
            JsonNode customProcessed = processor.processStringFields(
                    originalNode, text -> text.trim().toLowerCase());
            System.out.println("\nCustom Processed (Trim + Lowercase):");
            System.out.println(mapper.writerWithDefaultPrettyPrinter().writeValueAsString(customProcessed));

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
