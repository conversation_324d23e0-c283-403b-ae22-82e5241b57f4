package com.arihantcapital.oneclick.utils;

import com.opencsv.CSVReader;
import com.opencsv.CSVReaderBuilder;
import com.opencsv.CSVWriter;
import com.opencsv.exceptions.CsvException;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;
import org.springframework.web.multipart.MultipartFile;

public class CSVHelper {

    private static final String CSV_TYPE = "text/csv";
    private static final String[] CSV_HEADERS = {"col1", "col2", "col3"}; // Define your headers

    /**
     * Check if file is CSV format
     */
    public static boolean hasCSVFormat(MultipartFile file) {
        return CSV_TYPE.equals(file.getContentType())
                || file.getOriginalFilename().toLowerCase().endsWith(".csv");
    }

    /**
     * Read CSV from MultipartFile and return as List of Maps
     */
    public static List<Map<String, String>> readCSVFromMultipartFile(MultipartFile file) throws IOException {
        try (InputStream inputStream = file.getInputStream();
                InputStreamReader reader = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
                CSVReader csvReader = new CSVReaderBuilder(reader)
                        .withSkipLines(0) // Don't skip header
                        .build()) {

            List<String[]> records = csvReader.readAll();
            if (records.isEmpty()) {
                return new ArrayList<>();
            }

            // First row as headers (trim whitespace)
            String[] headers = Arrays.stream(records.get(0)).map(String::trim).toArray(String[]::new);

            // Convert remaining rows to List of Maps
            return records.stream()
                    .skip(1) // Skip header row
                    .map(row -> {
                        Map<String, String> rowMap = new HashMap<>();
                        for (int i = 0; i < headers.length && i < row.length; i++) {
                            rowMap.put(headers[i], row[i].trim());
                        }
                        return rowMap;
                    })
                    .collect(Collectors.toList());
        } catch (CsvException e) {
            throw new IOException("Error reading CSV file", e);
        }
    }

    /**
     * Read CSV from file path and return as List of Maps
     */
    public static List<Map<String, String>> readCSVFromFile(String filePath) throws IOException {
        Path path = Paths.get(filePath);
        try (Reader reader = Files.newBufferedReader(path, StandardCharsets.UTF_8);
                CSVReader csvReader =
                        new CSVReaderBuilder(reader).withSkipLines(0).build()) {

            List<String[]> records = csvReader.readAll();
            if (records.isEmpty()) {
                return new ArrayList<>();
            }

            // First row as headers (trim whitespace)
            String[] headers = Arrays.stream(records.get(0)).map(String::trim).toArray(String[]::new);

            // Convert remaining rows to List of Maps
            return records.stream()
                    .skip(1) // Skip header row
                    .map(row -> {
                        Map<String, String> rowMap = new HashMap<>();
                        for (int i = 0; i < headers.length && i < row.length; i++) {
                            rowMap.put(headers[i], row[i] != null ? row[i].trim() : "");
                        }
                        return rowMap;
                    })
                    .collect(Collectors.toList());
        } catch (CsvException e) {
            throw new IOException("Error reading CSV file", e);
        }
    }

    /**
     * Read CSV and return as List of String arrays (raw data)
     */
    public static List<String[]> readCSVAsRawData(MultipartFile file) throws IOException {
        try (InputStream inputStream = file.getInputStream();
                InputStreamReader reader = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
                CSVReader csvReader = new CSVReader(reader)) {

            return csvReader.readAll();
        } catch (CsvException e) {
            throw new IOException("Error reading CSV file", e);
        }
    }

    /**
     * Read CSV with custom delimiter
     */
    public static List<Map<String, String>> readCSVWithCustomDelimiter(MultipartFile file, char delimiter)
            throws IOException {
        try (InputStream inputStream = file.getInputStream();
                InputStreamReader reader = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
                CSVReader csvReader = new CSVReaderBuilder(reader)
                        .withCSVParser(new com.opencsv.CSVParserBuilder()
                                .withSeparator(delimiter)
                                .build())
                        .build()) {

            List<String[]> records = csvReader.readAll();
            if (records.isEmpty()) {
                return new ArrayList<>();
            }

            String[] headers = Arrays.stream(records.get(0)).map(String::trim).toArray(String[]::new);

            return records.stream()
                    .skip(1)
                    .map(row -> {
                        Map<String, String> rowMap = new HashMap<>();
                        for (int i = 0; i < headers.length && i < row.length; i++) {
                            rowMap.put(headers[i], row[i] != null ? row[i].trim() : "");
                        }
                        return rowMap;
                    })
                    .collect(Collectors.toList());
        } catch (CsvException e) {
            throw new IOException("Error reading CSV file", e);
        }
    }

    /**
     * Write List of Maps to CSV file
     */
    public static void writeCSVToFile(List<Map<String, String>> data, String filePath, String[] headers)
            throws IOException {
        try (FileWriter fileWriter = new FileWriter(filePath, StandardCharsets.UTF_8);
                CSVWriter csvWriter = new CSVWriter(fileWriter)) {

            // Write headers
            csvWriter.writeNext(headers);

            // Write data rows
            for (Map<String, String> row : data) {
                String[] rowData = new String[headers.length];
                for (int i = 0; i < headers.length; i++) {
                    rowData[i] = row.getOrDefault(headers[i], "");
                }
                csvWriter.writeNext(rowData);
            }
        }
    }

    /**
     * Convert List of Objects to CSV (using reflection)
     */
    public static <T> void writeObjectsToCSV(List<T> objects, String filePath, String[] fieldNames) throws IOException {
        try (FileWriter fileWriter = new FileWriter(filePath, StandardCharsets.UTF_8);
                CSVWriter csvWriter = new CSVWriter(fileWriter)) {

            // Write headers
            csvWriter.writeNext(fieldNames);

            // Write data using reflection
            for (T obj : objects) {
                String[] rowData = new String[fieldNames.length];
                Class<?> clazz = obj.getClass();

                for (int i = 0; i < fieldNames.length; i++) {
                    try {
                        var field = clazz.getDeclaredField(fieldNames[i]);
                        field.setAccessible(true);
                        Object value = field.get(obj);
                        rowData[i] = value != null ? value.toString() : "";
                    } catch (Exception e) {
                        rowData[i] = "";
                    }
                }
                csvWriter.writeNext(rowData);
            }
        }
    }

    /**
     * Get CSV headers from file
     */
    public static String[] getCSVHeaders(MultipartFile file) throws IOException {
        try (InputStream inputStream = file.getInputStream();
                InputStreamReader reader = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
                CSVReader csvReader = new CSVReader(reader)) {

            String[] headers = csvReader.readNext();
            return headers != null ? Arrays.stream(headers).map(String::trim).toArray(String[]::new) : new String[0];
        } catch (CsvException e) {
            throw new IOException("Error reading CSV headers", e);
        }
    }

    /**
     * Validate CSV structure
     */
    public static boolean validateCSVStructure(MultipartFile file, String[] expectedHeaders) throws IOException {
        String[] actualHeaders = getCSVHeaders(file);
        return Arrays.equals(expectedHeaders, actualHeaders);
    }

    /**
     * Filter CSV data based on condition
     */
    public static List<Map<String, String>> filterCSVData(
            List<Map<String, String>> data, String columnName, String value) {
        return data.stream().filter(row -> value.equals(row.get(columnName))).collect(Collectors.toList());
    }

    /**
     * Group CSV data by column
     */
    public static Map<String, List<Map<String, String>>> groupCSVDataBy(
            List<Map<String, String>> data, String columnName) {
        return data.stream().collect(Collectors.groupingBy(row -> row.getOrDefault(columnName, "NULL")));
    }

    /**
     * Get unique values from a column
     */
    public static Set<String> getUniqueValues(List<Map<String, String>> data, String columnName) {
        return data.stream()
                .map(row -> row.get(columnName))
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    /**
     * Convert CSV to JSON string
     */
    public static String convertCSVToJSON(List<Map<String, String>> data) {
        // You can use Jackson or Gson here
        // This is a simple implementation
        StringBuilder json = new StringBuilder();
        json.append("[\n");

        for (int i = 0; i < data.size(); i++) {
            Map<String, String> row = data.get(i);
            json.append("  {\n");

            int j = 0;
            for (Map.Entry<String, String> entry : row.entrySet()) {
                json.append("    \"")
                        .append(entry.getKey())
                        .append("\": \"")
                        .append(entry.getValue())
                        .append("\"");
                if (++j < row.size()) {
                    json.append(",");
                }
                json.append("\n");
            }

            json.append("  }");
            if (i < data.size() - 1) {
                json.append(",");
            }
            json.append("\n");
        }

        json.append("]");
        return json.toString();
    }
}
