package com.arihantcapital.oneclick.utils;

import java.io.ByteArrayInputStream;
import java.math.BigInteger;
import java.security.KeyFactory;
import java.security.MessageDigest;
import java.security.PublicKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.RSAPublicKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import javax.crypto.Cipher;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

public class CryptographicUtils {

    /**
     * @param data The data to be encoded.
     * @return The SHA256 encoded value.
     * @private This method is for internal use.
     * <p>
     * Returns the SHA256 encoded value of the given data.
     */
    private String SHA256Encode(String data) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(data.getBytes("UTF-8"));
            return bytesToHex(hash);
        } catch (Exception e) {
            throw new RuntimeException("Error encoding SHA256", e);
        }
    }

    /**
     * @param publicKeyStringXML The XML string representing the public key.
     * @return The public key in PEM format.
     * @throws Exception if parsing fails
     * @private This method is for internal use.
     * <p>
     * Parses a public key in XML format and returns it in PEM format.
     */
    private String parsePublicKeyStringXML(String publicKeyStringXML) throws Exception {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document doc = builder.parse(new ByteArrayInputStream(publicKeyStringXML.getBytes()));

            Element root = doc.getDocumentElement();
            NodeList modulusNodes = root.getElementsByTagName("Modulus");
            NodeList exponentNodes = root.getElementsByTagName("Exponent");

            if (modulusNodes.getLength() == 0 || exponentNodes.getLength() == 0) {
                throw new Exception("XML::PARSE_FAILED::PUBLIC_KEY");
            }

            String modulusBase64 = modulusNodes.item(0).getTextContent();
            String exponentBase64 = exponentNodes.item(0).getTextContent();

            byte[] modulusBytes = Base64.getDecoder().decode(modulusBase64);
            byte[] exponentBytes = Base64.getDecoder().decode(exponentBase64);

            BigInteger modulus = new BigInteger(1, modulusBytes);
            BigInteger exponent = new BigInteger(1, exponentBytes);

            // Create RSA public key
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            RSAPublicKeySpec keySpec = new RSAPublicKeySpec(modulus, exponent);
            RSAPublicKey publicKey = (RSAPublicKey) keyFactory.generatePublic(keySpec);

            // Convert to PEM format
            return convertToPEM(publicKey);

        } catch (Exception e) {
            throw new Exception("XML::PARSE_FAILED::PUBLIC_KEY");
        }
    }

    /**
     * @param data            The data to be encrypted.
     * @param publicKeyString The RSA public key in PEM format.
     * @return The encrypted data in base64 format.
     * @private This method is for internal use.
     * <p>
     * Encrypts the given data using the RSA public key.
     */
    private String RSAEncrypt(String data, String publicKeyString) {
        try {
            // Parse PEM format public key
            PublicKey publicKey = parsePublicKeyFromPEM(publicKeyString);

            // Encrypt using RSA with PKCS1 padding
            Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);

            byte[] encryptedBytes = cipher.doFinal(data.getBytes("UTF-8"));
            return Base64.getEncoder().encodeToString(encryptedBytes);

        } catch (Exception e) {
            throw new RuntimeException("Error encrypting data", e);
        }
    }

    /**
     * @param data The data to be encoded.
     * @return The MD5 hash of the data.
     * @private This method is for internal use.
     * <p>
     * Generates an MD5 hash for the given data.
     */
    private String MD5Encode(String data) {
        try {
            MessageDigest digest = MessageDigest.getInstance("MD5");
            byte[] hash = digest.digest(data.getBytes("UTF-8"));
            return bytesToHex(hash);
        } catch (Exception e) {
            throw new RuntimeException("Error encoding MD5", e);
        }
    }

    // Helper method to convert bytes to hex string
    private String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }

    // Helper method to convert RSA public key to PEM format
    private String convertToPEM(RSAPublicKey publicKey) {
        try {
            byte[] encoded = publicKey.getEncoded();
            String base64 = Base64.getEncoder().encodeToString(encoded);

            StringBuilder pem = new StringBuilder();
            pem.append("-----BEGIN PUBLIC KEY-----\n");

            // Split base64 string into 64-character lines
            for (int i = 0; i < base64.length(); i += 64) {
                int end = Math.min(i + 64, base64.length());
                pem.append(base64.substring(i, end)).append("\n");
            }

            pem.append("-----END PUBLIC KEY-----");
            return pem.toString();

        } catch (Exception e) {
            throw new RuntimeException("Error converting to PEM format", e);
        }
    }

    // Helper method to parse public key from PEM format
    private PublicKey parsePublicKeyFromPEM(String pemKey) {
        try {
            String publicKeyPEM = pemKey.replace("-----BEGIN PUBLIC KEY-----", "")
                    .replace("-----END PUBLIC KEY-----", "")
                    .replace("-----BEGIN RSA PUBLIC KEY-----", "")
                    .replace("-----END RSA PUBLIC KEY-----", "")
                    .replaceAll("\\s", "");

            byte[] decoded = Base64.getDecoder().decode(publicKeyPEM);
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(decoded);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");

            return keyFactory.generatePublic(keySpec);

        } catch (Exception e) {
            throw new RuntimeException("Error parsing PEM public key", e);
        }
    }
}
