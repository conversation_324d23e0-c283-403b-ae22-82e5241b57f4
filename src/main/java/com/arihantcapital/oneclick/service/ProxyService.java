package com.arihantcapital.oneclick.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Service
public class ProxyService {

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();

    public JsonNode proxyRequest(String url, JsonNode payload, HttpHeaders headers) throws Exception {
        try {
            log.info("Proxy Request Received for URL: {}", url);
            log.info("Proxy Request Received Headers: {}", headers);
            log.info("Proxy Request Received Payload: {}", payload);

            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setAll(headers.toSingleValueMap());
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.set("x-real-ip", "");
            httpHeaders.set("x-forwarded-for", "");

            log.info("Proxy Request Headers: {}", httpHeaders);

            HttpEntity<String> requestEntity = new HttpEntity<>(objectMapper.writeValueAsString(payload), httpHeaders);
            ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
            String responseString = response.getBody();
            log.info("Proxy Response: {}", responseString);
            if (responseString == null) {
                throw new Exception("Empty response received from server");
            }
            return objectMapper.readTree(responseString);
        } catch (Exception e) {
            JsonNode errorNode = objectMapper.createObjectNode().put("error", e.getMessage());
            log.info("Error Node {}", errorNode);

            log.error(e.getMessage());
            throw e;
        }
    }
}
