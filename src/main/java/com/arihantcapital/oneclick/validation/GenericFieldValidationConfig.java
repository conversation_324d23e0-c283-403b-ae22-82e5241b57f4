package com.arihantcapital.oneclick.validation;

import com.arihantcapital.oneclick.enums.ServiceType;
import java.util.HashMap;
import java.util.Map;

public class GenericFieldValidationConfig<T> {
    private final String fieldName;
    private final Map<ServiceType, GenericValidationRule<T>> serviceRules;

    public GenericFieldValidationConfig(String fieldName) {
        this.fieldName = fieldName;
        this.serviceRules = new HashMap<>();
    }

    public GenericFieldValidationConfig<T> addRule(ServiceType service, GenericValidationRule<T> rule) {
        this.serviceRules.put(service, rule);
        return this;
    }

    public GenericValidationRule<T> getRule(ServiceType service) {
        return serviceRules.get(service);
    }

    public String getFieldName() {
        return fieldName;
    }
}
