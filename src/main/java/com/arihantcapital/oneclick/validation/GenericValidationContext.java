package com.arihantcapital.oneclick.validation;

import com.arihantcapital.oneclick.enums.ServiceType;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

public class GenericValidationContext<T> {
    private final T data;
    private final ServiceType serviceType;
    private final Map<String, Object> fieldValues;
    private final Class<T> dataClass;

    public GenericValidationContext(T data, ServiceType serviceType) {
        this.data = data;
        this.serviceType = serviceType;
        this.dataClass = (Class<T>) data.getClass();
        this.fieldValues = extractFieldValues(data);
    }

    private Map<String, Object> extractFieldValues(T data) {
        Map<String, Object> values = new HashMap<>();
        Field[] fields = dataClass.getDeclaredFields();

        for (Field field : fields) {
            try {
                field.setAccessible(true);
                values.put(field.getName(), field.get(data));
            } catch (IllegalAccessException e) {
                values.put(field.getName(), null);
            }
        }
        return values;
    }

    public Object getFieldValue(String fieldName) {
        return fieldValues.get(fieldName);
    }

    public String getStringValue(String fieldName) {
        Object value = getFieldValue(fieldName);
        return value != null ? value.toString() : null;
    }

    public Double getDoubleValue(String fieldName) {
        Object value = getFieldValue(fieldName);
        if (value instanceof Double) {
            return (Double) value;
        } else if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        return null;
    }

    public Integer getIntegerValue(String fieldName) {
        Object value = getFieldValue(fieldName);
        if (value instanceof Integer) {
            return (Integer) value;
        } else if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return null;
    }

    public Boolean getBooleanValue(String fieldName) {
        Object value = getFieldValue(fieldName);
        return value instanceof Boolean ? (Boolean) value : null;
    }

    public boolean hasValue(String fieldName) {
        Object value = getFieldValue(fieldName);
        return value != null && !value.toString().trim().isEmpty();
    }

    public boolean isEmpty(String fieldName) {
        return !hasValue(fieldName);
    }

    public ServiceType getServiceType() {
        return serviceType;
    }

    public T getData() {
        return data;
    }

    public Class<T> getDataClass() {
        return dataClass;
    }

    public Map<String, Object> getAllFieldValues() {
        return new HashMap<>(fieldValues);
    }
}
