package com.arihantcapital.oneclick.validation;

import java.util.Collections;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Type-safe registry for validation configurations.
 * This registry maintains validation configurations for different data types
 * and provides thread-safe access to them.
 */
public class ValidationConfigurationRegistry {
    private final Map<Class<?>, Map<String, GenericFieldValidationConfig<?>>> configurations =
            new ConcurrentHashMap<>();

    /**
     * Retrieves the validation configuration for a specific class.
     *
     * @param clazz the class to get configuration for
     * @param <T> the type of the class
     * @return the validation configuration map, or null if not found
     * @throws IllegalArgumentException if clazz is null
     */
    public <T> Map<String, GenericFieldValidationConfig<T>> getConfiguration(Class<T> clazz) {
        if (clazz == null) {
            throw new IllegalArgumentException("Class cannot be null");
        }

        Map<String, GenericFieldValidationConfig<?>> rawConfig = configurations.get(clazz);
        if (rawConfig == null) {
            return null;
        }

        // Type-safe conversion - we know the types match because we control registration
        return createTypeSafeMap(rawConfig);
    }

    /**
     * Registers a validation configuration for a specific class.
     *
     * @param clazz the class to register configuration for
     * @param config the validation configuration map
     * @param <T> the type of the class
     * @throws IllegalArgumentException if clazz or config is null
     */
    public <T> void registerConfiguration(Class<T> clazz, Map<String, GenericFieldValidationConfig<T>> config) {
        if (clazz == null) {
            throw new IllegalArgumentException("Class cannot be null");
        }
        if (config == null) {
            throw new IllegalArgumentException("Configuration cannot be null");
        }

        // Create a type-erased copy for storage
        Map<String, GenericFieldValidationConfig<?>> erasedConfig = new ConcurrentHashMap<>();
        for (Map.Entry<String, GenericFieldValidationConfig<T>> entry : config.entrySet()) {
            if (entry.getKey() == null) {
                throw new IllegalArgumentException("Field name cannot be null");
            }
            if (entry.getValue() == null) {
                throw new IllegalArgumentException("Field validation config cannot be null");
            }
            erasedConfig.put(entry.getKey(), entry.getValue());
        }

        configurations.put(clazz, erasedConfig);
    }

    /**
     * Checks if a configuration exists for the given class.
     *
     * @param clazz the class to check
     * @return true if configuration exists, false otherwise
     */
    public boolean hasConfiguration(Class<?> clazz) {
        return clazz != null && configurations.containsKey(clazz);
    }

    /**
     * Checks if a specific field configuration exists for the given class.
     *
     * @param clazz the class to check
     * @param fieldName the field name to check
     * @return true if field configuration exists, false otherwise
     */
    public boolean hasFieldConfiguration(Class<?> clazz, String fieldName) {
        if (clazz == null || fieldName == null) {
            return false;
        }

        Map<String, GenericFieldValidationConfig<?>> config = configurations.get(clazz);
        return config != null && config.containsKey(fieldName);
    }

    /**
     * Gets all registered classes.
     *
     * @return an unmodifiable set of all registered classes
     */
    public Set<Class<?>> getRegisteredClasses() {
        return Collections.unmodifiableSet(configurations.keySet());
    }

    /**
     * Removes the configuration for a specific class.
     *
     * @param clazz the class to remove configuration for
     * @return true if configuration was removed, false if it didn't exist
     */
    public boolean removeConfiguration(Class<?> clazz) {
        if (clazz == null) {
            return false;
        }
        return configurations.remove(clazz) != null;
    }

    /**
     * Clears all configurations.
     */
    public void clearAll() {
        configurations.clear();
    }

    /**
     * Gets the number of registered configurations.
     *
     * @return the number of registered configurations
     */
    public int size() {
        return configurations.size();
    }

    /**
     * Creates a type-safe map from the type-erased storage map.
     * This method is safe because we control the registration process
     * and ensure type consistency.
     */
    @SuppressWarnings("unchecked")
    private <T> Map<String, GenericFieldValidationConfig<T>> createTypeSafeMap(
            Map<String, GenericFieldValidationConfig<?>> rawConfig) {

        Map<String, GenericFieldValidationConfig<T>> typeSafeMap = new ConcurrentHashMap<>();
        for (Map.Entry<String, GenericFieldValidationConfig<?>> entry : rawConfig.entrySet()) {
            // This cast is safe because we ensure type consistency during registration
            GenericFieldValidationConfig<T> typedConfig = (GenericFieldValidationConfig<T>) entry.getValue();
            typeSafeMap.put(entry.getKey(), typedConfig);
        }
        return typeSafeMap;
    }
}
