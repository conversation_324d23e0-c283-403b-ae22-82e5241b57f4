package com.arihantcapital.oneclick.validation;

import com.arihantcapital.oneclick.enums.ServiceType;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import org.springframework.stereotype.Component;

@Component
public class GenericDataValidator {
    private final ValidationConfigurationRegistry registry;

    public GenericDataValidator() {
        this.registry = new ValidationConfigurationRegistry();
        setupDefaultConfigurations();
    }

    private void setupDefaultConfigurations() {
        // Setup CommonData configuration (your existing logic)
        setupCommonDataConfiguration();

        // You can add more default configurations here
        // setupUserProfileConfiguration();
        // setupOrderConfiguration();
    }

    private void setupCommonDataConfiguration() {
        Map<String, GenericFieldValidationConfig<CommonData>> fieldConfigs = new HashMap<>();

        // User ID validation
        fieldConfigs.put(
                "userId",
                new GenericFieldValidationConfig<CommonData>("userId")
                        .addRule(
                                ServiceType.USER_SERVICE,
                                new GenericValidationRule<CommonData>()
                                        .required(true)
                                        .minLength(5)
                                        .maxLength(20)
                                        .regexPattern("^[a-zA-Z0-9_]+$")
                                        .errorMessage("User ID must be 5-20 alphanumeric characters"))
                        .addRule(
                                ServiceType.ORDER_SERVICE,
                                new GenericValidationRule<CommonData>()
                                        .required(true)
                                        .minLength(5)
                                        .errorMessage("User ID is required for orders")));

        // Email validation with context-dependent rules
        fieldConfigs.put(
                "email",
                new GenericFieldValidationConfig<CommonData>("email")
                        .addRule(
                                ServiceType.USER_SERVICE,
                                new GenericValidationRule<CommonData>()
                                        .required(true)
                                        .regexPattern("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")
                                        .errorMessage("Valid email address is required"))
                        .addRule(
                                ServiceType.NOTIFICATION_SERVICE,
                                new GenericValidationRule<CommonData>()
                                        .required(true)
                                        .regexPattern("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")
                                        .contextualValidator(context -> {
                                            return context.hasValue("email") || context.hasValue("phone");
                                        })
                                        .errorMessage("Either email or phone is required for notifications")));

        // Amount validation with context-dependent limits
        fieldConfigs.put(
                "amount",
                new GenericFieldValidationConfig<CommonData>("amount")
                        .addRule(
                                ServiceType.PAYMENT_SERVICE,
                                new GenericValidationRule<CommonData>()
                                        .required(true)
                                        .contextualValidator(context -> {
                                            Double amount = context.getDoubleValue("amount");
                                            Boolean isVip = context.getBooleanValue("isVipCustomer");
                                            String paymentMethod = context.getStringValue("paymentMethod");

                                            if (amount == null || amount <= 0) return false;

                                            double maxLimit = Boolean.TRUE.equals(isVip) ? 50000 : 10000;

                                            if ("CREDIT_CARD".equals(paymentMethod)) {
                                                maxLimit *= 1.5;
                                            }

                                            return amount <= maxLimit;
                                        })
                                        .errorMessage(
                                                "Amount exceeds allowed limit based on customer type and payment method")));

        registry.registerConfiguration(CommonData.class, fieldConfigs);
    }

    // Method to register custom validation configuration for any class
    public <T> void registerValidationConfiguration(
            Class<T> clazz, Map<String, GenericFieldValidationConfig<T>> config) {
        registry.registerConfiguration(clazz, config);
    }

    private <T> List<String> validateField(
            String fieldName, Object value, GenericValidationRule<T> rule, GenericValidationContext<T> context) {
        List<String> errors = new ArrayList<>();

        // Check if this is a conditional rule
        if (rule instanceof GenericConditionalValidationRule) {
            GenericConditionalValidationRule<T> conditionalRule = (GenericConditionalValidationRule<T>) rule;
            if (!conditionalRule.shouldApply(context)) {
                return errors;
            }
        }

        // Contextual validation
        if (rule.getContextualValidator() != null
                && !rule.getContextualValidator().validate(context)) {
            errors.add(
                    rule.getErrorMessage() != null
                            ? rule.getErrorMessage()
                            : fieldName + " failed contextual validation");
            return errors;
        }

        // Required field validation
        if (rule.isRequired() && (value == null || value.toString().trim().isEmpty())) {
            errors.add(rule.getErrorMessage() != null ? rule.getErrorMessage() : fieldName + " is required");
            return errors;
        }

        // Skip other validations if value is null/empty and not required
        if (value == null || value.toString().trim().isEmpty()) {
            return errors;
        }

        String stringValue = value.toString();

        // Length validations
        if (rule.getMinLength() != null && stringValue.length() < rule.getMinLength()) {
            errors.add(
                    rule.getErrorMessage() != null
                            ? rule.getErrorMessage()
                            : fieldName + " must be at least " + rule.getMinLength() + " characters");
        }

        if (rule.getMaxLength() != null && stringValue.length() > rule.getMaxLength()) {
            errors.add(
                    rule.getErrorMessage() != null
                            ? rule.getErrorMessage()
                            : fieldName + " must be at most " + rule.getMaxLength() + " characters");
        }

        // Regex validation
        if (rule.getRegexPattern() != null && !Pattern.matches(rule.getRegexPattern(), stringValue)) {
            errors.add(rule.getErrorMessage() != null ? rule.getErrorMessage() : fieldName + " format is invalid");
        }

        // Allowed values validation
        if (rule.getAllowedValues() != null && !rule.getAllowedValues().contains(value)) {
            errors.add(
                    rule.getErrorMessage() != null
                            ? rule.getErrorMessage()
                            : fieldName + " must be one of: " + rule.getAllowedValues());
        }

        // Custom validator
        if (rule.getCustomValidator() != null && !rule.getCustomValidator().test(value)) {
            errors.add(
                    rule.getErrorMessage() != null ? rule.getErrorMessage() : fieldName + " failed custom validation");
        }

        return errors;
    }

    public <T> Map<String, List<String>> validate(T data, ServiceType serviceType) {
        Map<String, List<String>> validationErrors = new HashMap<>();

        @SuppressWarnings("unchecked")
        Class<T> dataClass = (Class<T>) data.getClass();

        Map<String, GenericFieldValidationConfig<T>> fieldConfigs = registry.getConfiguration(dataClass);

        if (fieldConfigs == null) {
            throw new IllegalArgumentException("No validation configuration found for class: " + dataClass.getName());
        }

        GenericValidationContext<T> context = new GenericValidationContext<>(data, serviceType);

        for (Map.Entry<String, GenericFieldValidationConfig<T>> entry : fieldConfigs.entrySet()) {
            String fieldName = entry.getKey();
            GenericFieldValidationConfig<T> config = entry.getValue();
            GenericValidationRule<T> rule = config.getRule(serviceType);

            if (rule != null) {
                Object value = context.getFieldValue(fieldName);
                List<String> fieldErrors = validateField(fieldName, value, rule, context);
                if (!fieldErrors.isEmpty()) {
                    validationErrors.put(fieldName, fieldErrors);
                }
            }
        }

        return validationErrors;
    }

    public <T> void validateAndThrow(T data, ServiceType serviceType) throws ValidationError {
        Map<String, List<String>> errors = validate(data, serviceType);
        if (!errors.isEmpty()) {
            Map.Entry<String, List<String>> firstError =
                    errors.entrySet().iterator().next();
            throw new ValidationError(firstError.getKey(), firstError.getValue().get(0));
        }
    }

    public <T> boolean isValid(T data, ServiceType serviceType) {
        return validate(data, serviceType).isEmpty();
    }

    /**
     * Get the validation configuration registry.
     * This method is primarily for testing purposes.
     *
     * @return the validation configuration registry
     */
    public ValidationConfigurationRegistry getRegistry() {
        return registry;
    }
}
