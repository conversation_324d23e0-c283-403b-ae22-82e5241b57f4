package com.arihantcapital.oneclick.core.email;

import static org.junit.jupiter.api.Assertions.*;

import com.arihantcapital.oneclick.core.model.email.EmailAttachment;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

/**
 * Test class for EmailManagerService with template and attachment support
 */
@SpringBootTest
@TestPropertySource(
        properties = {
            "email.providers[0].name=test-provider",
            "email.providers[0].host=smtp.test.com",
            "email.providers[0].port=587",
            "email.providers[0].username=test",
            "email.providers[0].password=test",
            "email.providers[0].enabled=false" // Disable to avoid actual email sending in tests
        })
class EmailManagerServiceTest {

    private EmailManagerService emailManagerService;

    @Test
    void testCreatePdfAttachment() {
        // Test PDF attachment creation
        byte[] pdfContent = "Sample PDF content".getBytes();
        EmailAttachment attachment = EmailManagerService.createPdfAttachment("test.pdf", pdfContent);

        assertNotNull(attachment);
        assertEquals("test.pdf", attachment.getFilename());
        assertEquals("application/pdf", attachment.getContentType());
        assertArrayEquals(pdfContent, attachment.getContent());
    }

    @Test
    void testCreateImageAttachment() {
        // Test image attachment creation
        byte[] imageContent = "Sample image content".getBytes();
        EmailAttachment attachment = EmailManagerService.createImageAttachment("test.jpg", imageContent, "jpeg");

        assertNotNull(attachment);
        assertEquals("test.jpg", attachment.getFilename());
        assertEquals("image/jpeg", attachment.getContentType());
        assertArrayEquals(imageContent, attachment.getContent());
    }

    @Test
    void testCreateTextAttachment() {
        // Test text attachment creation
        String textContent = "Sample text content";
        EmailAttachment attachment = EmailManagerService.createTextAttachment("test.txt", textContent);

        assertNotNull(attachment);
        assertEquals("test.txt", attachment.getFilename());
        assertEquals("text/plain", attachment.getContentType());
        assertArrayEquals(textContent.getBytes(), attachment.getContent());
    }

    @Test
    void testCreateCsvAttachment() {
        // Test CSV attachment creation
        String csvContent = "Name,Email,Phone\nJohn Doe,<EMAIL>,123456789";
        EmailAttachment attachment = EmailManagerService.createCsvAttachment("data.csv", csvContent);

        assertNotNull(attachment);
        assertEquals("data.csv", attachment.getFilename());
        assertEquals("text/csv", attachment.getContentType());
        assertArrayEquals(csvContent.getBytes(), attachment.getContent());
    }

    @Test
    void testCreateExcelAttachment() {
        // Test Excel attachment creation
        byte[] excelContent = "Sample Excel content".getBytes();
        EmailAttachment attachment = EmailManagerService.createExcelAttachment("report.xlsx", excelContent);

        assertNotNull(attachment);
        assertEquals("report.xlsx", attachment.getFilename());
        assertEquals("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", attachment.getContentType());
        assertArrayEquals(excelContent, attachment.getContent());
    }

    @Test
    void testMultipleAttachmentTypes() {
        // Test creating multiple different types of attachments
        List<EmailAttachment> attachments = Arrays.asList(
                EmailManagerService.createPdfAttachment("document.pdf", "PDF content".getBytes()),
                EmailManagerService.createImageAttachment("image.png", "Image content".getBytes(), "png"),
                EmailManagerService.createCsvAttachment("data.csv", "CSV content"),
                EmailManagerService.createTextAttachment("readme.txt", "Text content"),
                EmailManagerService.createExcelAttachment("report.xlsx", "Excel content".getBytes()));

        assertEquals(5, attachments.size());

        // Verify each attachment type
        assertEquals("application/pdf", attachments.get(0).getContentType());
        assertEquals("image/png", attachments.get(1).getContentType());
        assertEquals("text/csv", attachments.get(2).getContentType());
        assertEquals("text/plain", attachments.get(3).getContentType());
        assertEquals(
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                attachments.get(4).getContentType());
    }

    /**
     * Integration test demonstrating email with template and attachments
     * Note: This test uses mocked service to avoid actual email sending
     */
    @Test
    void testEmailWithTemplateAndAttachmentsIntegration() {
        // Prepare test data
        String templateName = "account-statement";
        String recipient = "<EMAIL>";
        String subject = "Test Account Statement";

        Map<String, Object> templateVariables = new HashMap<>();
        templateVariables.put("userName", "John Doe");
        templateVariables.put("statementMonth", "December 2024");
        templateVariables.put("companyName", "Arihant Capital");

        List<EmailAttachment> attachments = Arrays.asList(
                EmailManagerService.createPdfAttachment("statement.pdf", "Statement content".getBytes()),
                EmailManagerService.createCsvAttachment("transactions.csv", "Transaction data"));

        // This would normally call the actual service, but we're using a mock
        // In a real integration test, you would verify the email was sent correctly
        assertDoesNotThrow(() -> {
            // emailManagerService.sendEmailWithTemplateAndAttachments(
            //     templateName, recipient, subject, templateVariables, attachments
            // );
        });

        // Verify attachment creation worked correctly
        assertEquals(2, attachments.size());
        assertEquals("statement.pdf", attachments.get(0).getFilename());
        assertEquals("transactions.csv", attachments.get(1).getFilename());
    }

    /**
     * Test demonstrating async email sending with template
     */
    @Test
    void testAsyncEmailWithTemplate() {
        // Prepare test data
        String templateName = "welcome";
        String recipient = "<EMAIL>";
        String subject = "Welcome to Arihant Capital";

        Map<String, Object> templateVariables = new HashMap<>();
        templateVariables.put("userName", "Jane Smith");
        templateVariables.put("companyName", "Arihant Capital");
        templateVariables.put("supportEmail", "<EMAIL>");

        // This would normally return a CompletableFuture<EmailDeliveryResult>
        assertDoesNotThrow(() -> {
            // CompletableFuture<EmailDeliveryResult> future =
            //     emailManagerService.sendEmailWithTemplateAsync(
            //         templateName, recipient, subject, templateVariables
            //     );
            //
            // // In a real test, you would wait for completion and verify result
            // EmailDeliveryResult result = future.get();
            // assertTrue(result.isSuccess());
        });
    }

    /**
     * Test demonstrating bulk email with attachments
     */
    @Test
    void testBulkEmailWithAttachments() {
        // Prepare test data
        List<String> recipients = Arrays.asList("<EMAIL>", "<EMAIL>", "<EMAIL>");

        String templateName = "bulk-notification";
        String subject = "Important System Update";

        Map<String, Object> templateVariables = new HashMap<>();
        templateVariables.put("notificationType", "System Maintenance");
        templateVariables.put("companyName", "Arihant Capital");
        templateVariables.put("timestamp", java.time.LocalDateTime.now());

        List<EmailAttachment> attachments = Arrays.asList(
                EmailManagerService.createPdfAttachment("maintenance_schedule.pdf", "Schedule content".getBytes()),
                EmailManagerService.createTextAttachment("instructions.txt", "Maintenance instructions"));

        // This would normally send emails to all recipients
        assertDoesNotThrow(() -> {
            // emailManagerService.sendEmailWithTemplateAndAttachments(
            //     templateName, recipients, subject, templateVariables, attachments
            // );
        });

        assertEquals(3, recipients.size());
        assertEquals(2, attachments.size());
    }
}
