package com.arihantcapital.oneclick.validation;

import static org.junit.jupiter.api.Assertions.*;

import com.arihantcapital.oneclick.enums.ServiceType;
import com.arihantcapital.oneclick.service.GenericValidationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * Unit tests for the Generic Validation Framework.
 * These tests run without Spring context and test the validation logic directly.
 */
public class GenericValidationUnitTest {

    private GenericDataValidator dataValidator;
    private GenericValidationService validationService;

    @BeforeEach
    void setUp() {
        // Create instances manually without Spring context
        dataValidator = new GenericDataValidator();
        validationService = new GenericValidationService();

        // Manually inject the dataValidator into the service
        // Since we can't use reflection easily, we'll create a custom setup
        setupValidationService();

        // Setup UserProfile validation configuration manually
        setupUserProfileValidation();
    }

    private void setupValidationService() {
        // We need to create a custom validation service that uses our dataValidator
        // For now, we'll test the dataValidator directly
    }

    private void setupUserProfileValidation() {
        // Setup UserProfile validation configuration
        UserProfileValidationConfiguration config = new UserProfileValidationConfiguration();
        // We need to manually set up the validation rules since @PostConstruct won't run
        setupUserProfileValidationManually();
    }

    private void setupUserProfileValidationManually() {
        // Manually setup UserProfile validation rules
        java.util.Map<String, GenericFieldValidationConfig<UserProfile>> fieldConfigs = new java.util.HashMap<>();

        // Username validation
        fieldConfigs.put(
                "username",
                new GenericFieldValidationConfig<UserProfile>("username")
                        .addRule(
                                ServiceType.USER_SERVICE,
                                new GenericValidationRule<UserProfile>()
                                        .required(true)
                                        .minLength(3)
                                        .maxLength(20)
                                        .regexPattern("^[a-zA-Z0-9_]+$")
                                        .contextualValidator(context -> {
                                            String username = context.getStringValue("username");
                                            return username != null && !isUsernameTaken(username);
                                        })
                                        .errorMessage("Username must be 3-20 alphanumeric characters and unique")));

        // Age validation
        fieldConfigs.put(
                "age",
                new GenericFieldValidationConfig<UserProfile>("age")
                        .addRule(
                                ServiceType.USER_SERVICE,
                                new GenericValidationRule<UserProfile>()
                                        .required(true)
                                        .customValidator(value -> {
                                            if (value instanceof Integer) {
                                                int age = (Integer) value;
                                                return age >= 13 && age <= 120;
                                            }
                                            return false;
                                        })
                                        .errorMessage("Age must be between 13 and 120")));

        // SSN validation for business profiles
        fieldConfigs.put(
                "socialSecurityNumber",
                new GenericFieldValidationConfig<UserProfile>("socialSecurityNumber")
                        .addRule(
                                ServiceType.USER_SERVICE,
                                new GenericConditionalValidationRule<UserProfile>()
                                        .when(context -> "BUSINESS".equals(context.getStringValue("profileType")))
                                        .required(true)
                                        .regexPattern("^\\d{3}-\\d{2}-\\d{4}$")
                                        .errorMessage("Valid SSN required for business profiles")));

        // Language validation with age restrictions
        fieldConfigs.put(
                "preferredLanguage",
                new GenericFieldValidationConfig<UserProfile>("preferredLanguage")
                        .addRule(
                                ServiceType.USER_SERVICE,
                                new GenericValidationRule<UserProfile>()
                                        .contextualValidator(context -> {
                                            String language = context.getStringValue("preferredLanguage");
                                            Integer age = context.getIntegerValue("age");

                                            if (language == null) return true; // Optional field

                                            // Age-restricted languages
                                            if (age != null && age < 18 && "ADULT_CONTENT_LANG".equals(language)) {
                                                return false;
                                            }

                                            return java.util.Arrays.asList(
                                                            "EN", "ES", "FR", "DE", "IT", "ADULT_CONTENT_LANG")
                                                    .contains(language);
                                        })
                                        .errorMessage("Invalid language or age-restricted language selected")));

        dataValidator.registerValidationConfiguration(UserProfile.class, fieldConfigs);
    }

    private boolean isUsernameTaken(String username) {
        // Simulate database check
        return "admin".equals(username) || "root".equals(username);
    }

    @Test
    public void testValidUserProfileValidation() {
        UserProfile profile = new UserProfile();
        profile.setUsername("john_doe");
        profile.setAge(25);
        profile.setProfileType("STANDARD");
        profile.setBio("This is a short bio");

        java.util.Map<String, java.util.List<String>> result =
                dataValidator.validate(profile, ServiceType.USER_SERVICE);

        assertTrue(result.isEmpty(), "Valid profile should pass validation");
    }

    @Test
    public void testInvalidUsernameValidation() {
        UserProfile profile = new UserProfile();
        profile.setUsername("ab"); // Too short
        profile.setAge(25);
        profile.setProfileType("STANDARD");

        java.util.Map<String, java.util.List<String>> result =
                dataValidator.validate(profile, ServiceType.USER_SERVICE);

        assertFalse(result.isEmpty(), "Profile with invalid username should fail validation");
        assertTrue(result.containsKey("username"), "Username validation error should be present");
    }

    @Test
    public void testReservedUsernameValidation() {
        UserProfile profile = new UserProfile();
        profile.setUsername("admin"); // Reserved username
        profile.setAge(25);
        profile.setProfileType("STANDARD");

        java.util.Map<String, java.util.List<String>> result =
                dataValidator.validate(profile, ServiceType.USER_SERVICE);

        assertFalse(result.isEmpty(), "Profile with reserved username should fail validation");
        assertTrue(result.containsKey("username"), "Username validation error should be present");
    }

    @Test
    public void testBusinessProfileRequiresSSN() {
        UserProfile profile = new UserProfile();
        profile.setUsername("business_user");
        profile.setAge(30);
        profile.setProfileType("BUSINESS");
        // No SSN provided

        java.util.Map<String, java.util.List<String>> result =
                dataValidator.validate(profile, ServiceType.USER_SERVICE);

        assertFalse(result.isEmpty(), "Business profile should require SSN");
        assertTrue(
                result.containsKey("socialSecurityNumber"),
                "SSN validation error should be present for business profiles");
    }

    @Test
    public void testAgeRestrictedLanguage() {
        UserProfile profile = new UserProfile();
        profile.setUsername("young_user");
        profile.setAge(16);
        profile.setPreferredLanguage("ADULT_CONTENT_LANG");

        java.util.Map<String, java.util.List<String>> result =
                dataValidator.validate(profile, ServiceType.USER_SERVICE);

        assertFalse(result.isEmpty(), "Minors should not access adult content language");
        assertTrue(
                result.containsKey("preferredLanguage"),
                "Preferred language validation error should be present for minors");
    }

    @Test
    public void testInvalidAgeValidation() {
        UserProfile profile = new UserProfile();
        profile.setUsername("test_user");
        profile.setAge(150); // Invalid age
        profile.setProfileType("STANDARD");

        java.util.Map<String, java.util.List<String>> result =
                dataValidator.validate(profile, ServiceType.USER_SERVICE);

        assertFalse(result.isEmpty(), "Profile with invalid age should fail validation");
        assertTrue(result.containsKey("age"), "Age validation error should be present");
    }

    @Test
    public void testCommonDataValidation() {
        CommonData paymentData = new CommonData();
        paymentData.setUserId("test_user_123");
        paymentData.setAmount(100.0);
        paymentData.setPaymentMethod("CREDIT_CARD");

        java.util.Map<String, java.util.List<String>> result =
                dataValidator.validate(paymentData, ServiceType.PAYMENT_SERVICE);

        assertTrue(result.isEmpty(), "Valid CommonData should pass validation");
    }

    @Test
    public void testValidationRegistryFunctionality() {
        // Test that the registry is working correctly
        ValidationConfigurationRegistry registry = dataValidator.getRegistry();

        assertTrue(registry.hasConfiguration(UserProfile.class), "UserProfile should be registered");
        assertTrue(registry.hasConfiguration(CommonData.class), "CommonData should be registered");

        assertTrue(
                registry.hasFieldConfiguration(UserProfile.class, "username"),
                "Username field should be configured for UserProfile");
        assertTrue(
                registry.hasFieldConfiguration(UserProfile.class, "age"),
                "Age field should be configured for UserProfile");
    }
}
