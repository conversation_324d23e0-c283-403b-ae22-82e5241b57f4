package com.arihantcapital.oneclick.validation;

import static org.junit.jupiter.api.Assertions.*;

import com.arihantcapital.oneclick.enums.ServiceType;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * Comprehensive test suite for ValidationConfigurationRegistry
 * to verify type safety and proper functionality.
 */
public class ValidationConfigurationRegistryTest {

    private ValidationConfigurationRegistry registry;

    @BeforeEach
    void setUp() {
        registry = new ValidationConfigurationRegistry();
    }

    @Test
    void testRegisterAndRetrieveConfiguration() {
        // Create a test configuration
        Map<String, GenericFieldValidationConfig<UserProfile>> config = new HashMap<>();
        config.put(
                "username",
                new GenericFieldValidationConfig<UserProfile>("username")
                        .addRule(
                                ServiceType.USER_SERVICE,
                                new GenericValidationRule<UserProfile>()
                                        .required(true)
                                        .minLength(3)
                                        .errorMessage("Username is required")));

        // Register configuration
        registry.registerConfiguration(UserProfile.class, config);

        // Retrieve and verify
        Map<String, GenericFieldValidationConfig<UserProfile>> retrieved = registry.getConfiguration(UserProfile.class);

        assertNotNull(retrieved);
        assertTrue(retrieved.containsKey("username"));
        assertEquals("username", retrieved.get("username").getFieldName());
    }

    @Test
    void testHasConfiguration() {
        assertFalse(registry.hasConfiguration(UserProfile.class));

        Map<String, GenericFieldValidationConfig<UserProfile>> config = new HashMap<>();
        registry.registerConfiguration(UserProfile.class, config);

        assertTrue(registry.hasConfiguration(UserProfile.class));
    }

    @Test
    void testHasFieldConfiguration() {
        Map<String, GenericFieldValidationConfig<UserProfile>> config = new HashMap<>();
        config.put("username", new GenericFieldValidationConfig<>("username"));

        registry.registerConfiguration(UserProfile.class, config);

        assertTrue(registry.hasFieldConfiguration(UserProfile.class, "username"));
        assertFalse(registry.hasFieldConfiguration(UserProfile.class, "nonexistent"));
        assertFalse(registry.hasFieldConfiguration(String.class, "username"));
    }

    @Test
    void testGetRegisteredClasses() {
        assertTrue(registry.getRegisteredClasses().isEmpty());

        Map<String, GenericFieldValidationConfig<UserProfile>> config1 = new HashMap<>();
        Map<String, GenericFieldValidationConfig<String>> config2 = new HashMap<>();

        registry.registerConfiguration(UserProfile.class, config1);
        registry.registerConfiguration(String.class, config2);

        Set<Class<?>> classes = registry.getRegisteredClasses();
        assertEquals(2, classes.size());
        assertTrue(classes.contains(UserProfile.class));
        assertTrue(classes.contains(String.class));
    }

    @Test
    void testRemoveConfiguration() {
        Map<String, GenericFieldValidationConfig<UserProfile>> config = new HashMap<>();
        registry.registerConfiguration(UserProfile.class, config);

        assertTrue(registry.hasConfiguration(UserProfile.class));
        assertTrue(registry.removeConfiguration(UserProfile.class));
        assertFalse(registry.hasConfiguration(UserProfile.class));
        assertFalse(registry.removeConfiguration(UserProfile.class)); // Already removed
    }

    @Test
    void testClearAll() {
        Map<String, GenericFieldValidationConfig<UserProfile>> config1 = new HashMap<>();
        Map<String, GenericFieldValidationConfig<String>> config2 = new HashMap<>();

        registry.registerConfiguration(UserProfile.class, config1);
        registry.registerConfiguration(String.class, config2);

        assertEquals(2, registry.size());

        registry.clearAll();

        assertEquals(0, registry.size());
        assertFalse(registry.hasConfiguration(UserProfile.class));
        assertFalse(registry.hasConfiguration(String.class));
    }

    @Test
    void testNullInputValidation() {
        // Test null class in getConfiguration
        assertThrows(IllegalArgumentException.class, () -> registry.getConfiguration(null));

        // Test null class in registerConfiguration
        Map<String, GenericFieldValidationConfig<UserProfile>> config = new HashMap<>();
        assertThrows(IllegalArgumentException.class, () -> registry.registerConfiguration(null, config));

        // Test null config in registerConfiguration
        assertThrows(IllegalArgumentException.class, () -> registry.registerConfiguration(UserProfile.class, null));

        // Test null field name in configuration
        Map<String, GenericFieldValidationConfig<UserProfile>> configWithNull = new HashMap<>();
        configWithNull.put(null, new GenericFieldValidationConfig<>("test"));
        assertThrows(
                IllegalArgumentException.class,
                () -> registry.registerConfiguration(UserProfile.class, configWithNull));

        // Test null field config in configuration
        Map<String, GenericFieldValidationConfig<UserProfile>> configWithNullValue = new HashMap<>();
        configWithNullValue.put("test", null);
        assertThrows(
                IllegalArgumentException.class,
                () -> registry.registerConfiguration(UserProfile.class, configWithNullValue));
    }

    @Test
    void testNullSafetyInUtilityMethods() {
        assertFalse(registry.hasConfiguration(null));
        assertFalse(registry.hasFieldConfiguration(null, "field"));
        assertFalse(registry.hasFieldConfiguration(UserProfile.class, null));
        assertFalse(registry.removeConfiguration(null));
    }

    @Test
    void testTypeSafety() {
        // Register configuration for UserProfile
        Map<String, GenericFieldValidationConfig<UserProfile>> userConfig = new HashMap<>();
        userConfig.put(
                "username",
                new GenericFieldValidationConfig<UserProfile>("username")
                        .addRule(ServiceType.USER_SERVICE, new GenericValidationRule<UserProfile>().required(true)));

        registry.registerConfiguration(UserProfile.class, userConfig);

        // Retrieve should maintain type safety
        Map<String, GenericFieldValidationConfig<UserProfile>> retrieved = registry.getConfiguration(UserProfile.class);

        assertNotNull(retrieved);
        GenericFieldValidationConfig<UserProfile> fieldConfig = retrieved.get("username");
        assertNotNull(fieldConfig);

        GenericValidationRule<UserProfile> rule = fieldConfig.getRule(ServiceType.USER_SERVICE);
        assertNotNull(rule);
        assertTrue(rule.isRequired());
    }

    @Test
    void testConcurrentAccess() {
        // This test verifies that the ConcurrentHashMap is working correctly
        Map<String, GenericFieldValidationConfig<UserProfile>> config = new HashMap<>();
        config.put("username", new GenericFieldValidationConfig<>("username"));

        registry.registerConfiguration(UserProfile.class, config);

        // Multiple threads accessing should not cause issues
        assertDoesNotThrow(() -> {
            for (int i = 0; i < 100; i++) {
                registry.hasConfiguration(UserProfile.class);
                registry.getConfiguration(UserProfile.class);
                registry.hasFieldConfiguration(UserProfile.class, "username");
            }
        });
    }

    @Test
    void testConfigurationIsolation() {
        // Test that configurations for different classes are isolated
        Map<String, GenericFieldValidationConfig<UserProfile>> userConfig = new HashMap<>();
        userConfig.put("username", new GenericFieldValidationConfig<>("username"));

        Map<String, GenericFieldValidationConfig<String>> stringConfig = new HashMap<>();
        stringConfig.put("value", new GenericFieldValidationConfig<>("value"));

        registry.registerConfiguration(UserProfile.class, userConfig);
        registry.registerConfiguration(String.class, stringConfig);

        // Verify isolation
        Map<String, GenericFieldValidationConfig<UserProfile>> retrievedUser =
                registry.getConfiguration(UserProfile.class);
        Map<String, GenericFieldValidationConfig<String>> retrievedString = registry.getConfiguration(String.class);

        assertNotNull(retrievedUser);
        assertNotNull(retrievedString);
        assertTrue(retrievedUser.containsKey("username"));
        assertFalse(retrievedUser.containsKey("value"));
        assertTrue(retrievedString.containsKey("value"));
        assertFalse(retrievedString.containsKey("username"));
    }
}
