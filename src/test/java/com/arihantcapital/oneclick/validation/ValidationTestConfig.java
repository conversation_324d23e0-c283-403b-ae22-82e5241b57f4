package com.arihantcapital.oneclick.validation;

import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.ComponentScan;

/**
 * Test configuration for validation framework tests.
 * This configuration ensures that only the validation framework components are loaded.
 */
@TestConfiguration
@ComponentScan(basePackages = {"com.arihantcapital.oneclick.validation.generic", "com.arihantcapital.oneclick.service"})
public class ValidationTestConfig {
    // This class provides component scanning for validation framework tests
}
