name: 'oneclick4j-monolith'
services:
  api:
    build:
      context: ./../..
      dockerfile: Dockerfile
    image: arihantcapital/oneclick4j-monolith:latest
    container_name: api
    environment:
      - APP_PORT=8080
      - SPRING_PROFILES_ACTIVE=prod
      - DB_URL=*****************************************
      - DB_USERNAME=postgres
      - DB_PASSWORD=postgres
      - SWAGGER_API_GATEWAY_URL=http://api-gateway:8989/catalog
      - MANAGEMENT_TRACING_ENABLED=true
      - MANAGEMENT_ZIPKIN_TRACING_ENDPOINT=http://tempo:9411
    ports:
      - "8080:8080"
    restart: unless-stopped
    labels:
      logging: "promtail"
    networks:
      - oneclick4j
networks:
  oneclick4j:
    external: true