name: 'oneclick4j-monolith'
services:
  postgres:
    image: postgres:latest
    restart: unless-stopped
    container_name: postgres
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=postgres
    ports:
      - "5432:5432"
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U postgres" ]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - oneclick4j
    volumes:
      - postgres:/var/lib/postgresql/data

  rabbitmq:
    image: rabbitmq:4.0.4-management
    container_name: rabbitmq
    environment:
      - RABBITMQ_DEFAULT_USER=admin
      - RABBITMQ_DEFAULT_PASS=admin
    ports:
      - "5672:5672"
      - "15672:15672"
    healthcheck:
      test: rabbitmq-diagnostics check_port_connectivity
      interval: 30s
      timeout: 30s
      retries: 10
    networks:
      - oneclick4j
    deploy:
      resources:
        limits:
          memory: 500m

  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: password
      PGADMIN_LISTEN_PORT: 80
    ports:
      - "15432:80"
    volumes:
      - pgadmin:/var/lib/pgadmin
    depends_on:
      - postgres
    networks:
      - oneclick4j

networks:
  oneclick4j:
    external: true

volumes:
  pgadmin: { }
  postgres: { }


