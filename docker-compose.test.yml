name: 'oneclick4j-monolith'
services:
  oneclick4j-monolith-db:
    image: postgres:17-alpine
    container_name: oneclick4j-monolith-db
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=oneclick4j
    ports:
      - "5432:5432"
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U postgres" ]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 500m

  bookstore-rabbitmq:
    image: rabbitmq:4.0.4-management
    container_name: bookstore-rabbitmq
    environment:
      - RABBITMQ_DEFAULT_USER=guest
      - RABBITMQ_DEFAULT_PASS=guest
    ports:
      - "5672:5672"
      - "15672:15672"
    healthcheck:
      test: rabbitmq-diagnostics check_port_connectivity
      interval: 30s
      timeout: 30s
      retries: 10
    deploy:
      resources:
        limits:
          memory: 500m

  oneclick4j-monolith-api:
    build:
      context: .
      dockerfile: Dockerfile
    image: arihantcapital/oneclick4j-monolith:latest
    container_name: api-service
    environment:
      - APP_PORT=8080
      - SPRING_PROFILES_ACTIVE=prod
      - DB_URL=*******************************************
      - DB_USERNAME=postgres
      - DB_PASSWORD=postgres
    ports:
      - "8080:8080"
    restart: unless-stopped
    labels:
      logging: "promtail"
    networks:
      - oneclick
networks:
  oneclick:
    external: true