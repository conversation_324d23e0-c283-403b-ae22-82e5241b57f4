# -------------------- Base Stage --------------------
FROM eclipse-temurin:21-jdk-alpine AS base

# Define port variable with default value
ENV APP_PORT=8080

# Add the labels
LABEL service_name="oneclick4j-monolith"
LABEL description="Oneclick4j Monolith Application"
LABEL version="0.0.1"
LABEL maintainer="Sa<PERSON><<EMAIL>>"
LABEL monitoring="prometheus"
LABEL logging="promtail"
LABEL metrics_port="${APP_PORT}"


# Install curl, timezone, and create non-root user
RUN apk add --no-cache curl wget tzdata bash && \
    addgroup -S appgroup && adduser -S appuser -G appgroup

ENV TZ=Asia/Kolkata
WORKDIR /app

# -------------------- Builder Stage --------------------
FROM maven:3.9.6-eclipse-temurin-21-alpine AS builder

WORKDIR /build

# Copy Maven project and source
COPY . .

# Build Spring Boot jar
RUN mvn clean package -DskipTests

# -------------------- Runner Stage --------------------
FROM base AS runner

# Copy built JAR and resources
COPY --from=builder /build/target/oneclick4j-0.0.1-SNAPSHOT.jar app.jar
COPY --from=builder /build/resources /app/resources

# Set permissions
RUN chown -R appuser:appgroup /app
USER appuser

# Define env and ports
ENV HOME=/home/<USER>
ENV SPRING_PROFILES_ACTIVE=docker
ENV JAVA_OPTS="-Xms768m -Xmx1024m -XX:+UseSerialGC"
EXPOSE ${APP_PORT}

# Optional: Docker health check using curl
HEALTHCHECK --interval=30s --timeout=5s --start-period=20s CMD curl -f http://localhost:${APP_PORT}/actuator/health || exit 1

# Run the application
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
