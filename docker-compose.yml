name: 'oneclick4j-monolith'
services:
  oneclick4j-monolith-api:
    build:
      context: .
      dockerfile: Dockerfile
    image: arihantcapital/oneclick4j-monolith:latest
    container_name: api-service
    environment:
      - APP_PORT=8080
      - SPRING_PROFILES_ACTIVE=prod
      - DB_URL=*******************************************
      - DB_USERNAME=postgres
      - DB_PASSWORD=postgres
    ports:
      - "8080:8080"
    restart: unless-stopped
    labels:
      logging: "promtail"
    networks:
      - oneclick
networks:
  oneclick:
    external: true