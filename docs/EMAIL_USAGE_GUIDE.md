# Email Service Usage Guide

This guide explains how to use the enhanced EmailManagerService with template and attachment support.

## Overview

The EmailManagerService now supports:
- ✅ Template-based email sending with Thymeleaf
- ✅ Multiple attachment types (PDF, Images, CSV, Excel, Text)
- ✅ Asynchronous email sending
- ✅ Bulk email sending
- ✅ Multiple email providers with failover
- ✅ Comprehensive logging and monitoring

## Basic Usage

### 1. Simple Template Email

```java
@Autowired
private EmailManagerService emailManagerService;

public void sendWelcomeEmail(String userEmail, String userName) {
    Map<String, Object> templateVariables = new HashMap<>();
    templateVariables.put("userName", userName);
    templateVariables.put("companyName", "Arihant Capital");
    
    EmailDeliveryResult result = emailManagerService.sendEmailWithTemplate(
        "welcome",                    // template name
        userEmail,                   // recipient
        "Welcome to Arihant Capital", // subject
        templateVariables            // template variables
    );
    
    if (result.isSuccess()) {
        log.info("Welcome email sent successfully");
    }
}
```

### 2. Email with Attachments

```java
public void sendAccountStatement(String userEmail, byte[] statementPdf) {
    Map<String, Object> templateVariables = new HashMap<>();
    templateVariables.put("userName", "John Doe");
    templateVariables.put("statementMonth", "December 2024");
    
    // Create PDF attachment
    EmailAttachment pdfAttachment = EmailManagerService.createPdfAttachment(
        "account_statement.pdf", 
        statementPdf
    );
    
    EmailDeliveryResult result = emailManagerService.sendEmailWithTemplateAndAttachments(
        "account-statement",
        userEmail,
        "Your Account Statement",
        templateVariables,
        Arrays.asList(pdfAttachment)
    );
}
```

### 3. Multiple Attachments

```java
public void sendKycDocuments(String userEmail, byte[] kycForm, byte[] panCard) {
    List<EmailAttachment> attachments = Arrays.asList(
        EmailManagerService.createPdfAttachment("kyc_form.pdf", kycForm),
        EmailManagerService.createImageAttachment("pan_card.jpg", panCard, "jpeg")
    );
    
    emailManagerService.sendEmailWithTemplateAndAttachments(
        "kyc-documents",
        userEmail,
        "KYC Documents",
        templateVariables,
        attachments
    );
}
```

## Attachment Types

### PDF Attachments
```java
EmailAttachment pdf = EmailManagerService.createPdfAttachment("document.pdf", pdfBytes);
```

### Image Attachments
```java
EmailAttachment image = EmailManagerService.createImageAttachment("photo.jpg", imageBytes, "jpeg");
```

### CSV Attachments
```java
String csvData = "Name,Email\nJohn,<EMAIL>";
EmailAttachment csv = EmailManagerService.createCsvAttachment("data.csv", csvData);
```

### Excel Attachments
```java
EmailAttachment excel = EmailManagerService.createExcelAttachment("report.xlsx", excelBytes);
```

### Text Attachments
```java
EmailAttachment text = EmailManagerService.createTextAttachment("readme.txt", "File content");
```

## Asynchronous Email Sending

### Single Recipient (Async)
```java
CompletableFuture<EmailDeliveryResult> future = emailManagerService.sendEmailWithTemplateAsync(
    "welcome",
    userEmail,
    "Welcome",
    templateVariables
);

// Handle result asynchronously
future.thenAccept(result -> {
    if (result.isSuccess()) {
        log.info("Email sent successfully");
    } else {
        log.error("Email failed: {}", result.getErrorMessage());
    }
});
```

### With Attachments (Async)
```java
CompletableFuture<EmailDeliveryResult> future = 
    emailManagerService.sendEmailWithTemplateAndAttachmentsAsync(
        "contract-note",
        userEmail,
        "Contract Note",
        templateVariables,
        attachments
    );
```

## Bulk Email Sending

### Multiple Recipients
```java
List<String> recipients = Arrays.asList(
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>"
);

EmailDeliveryResult result = emailManagerService.sendEmailWithTemplate(
    "bulk-notification",
    recipients,
    "Important Update",
    templateVariables
);
```

### Bulk with Attachments
```java
emailManagerService.sendEmailWithTemplateAndAttachments(
    "monthly-report",
    recipients,
    "Monthly Report",
    templateVariables,
    attachments
);
```

## Email Templates

Templates are stored in `src/main/resources/templates/email/` and use Thymeleaf syntax.

### Example Template (welcome.html)
```html
<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <title>Welcome</title>
</head>
<body>
    <h1>Welcome <span th:text="${userName}">User</span>!</h1>
    <p>Thank you for joining <span th:text="${companyName}">Company</span>.</p>
</body>
</html>
```

## Configuration

### Email Providers (application.properties)
```properties
# Primary provider
email.providers[0].name=sendgrid
email.providers[0].host=smtp.sendgrid.net
email.providers[0].port=587
email.providers[0].username=apikey
email.providers[0].password=${SENDGRID_API_KEY}
email.providers[0].priority=1
email.providers[0].enabled=true

# Backup provider
email.providers[1].name=backup-smtp
email.providers[1].host=smtp.backup.com
email.providers[1].port=587
email.providers[1].priority=2
email.providers[1].enabled=true
```

## Error Handling

```java
try {
    EmailDeliveryResult result = emailManagerService.sendEmailWithTemplate(
        templateName, recipient, subject, variables
    );
    
    if (!result.isSuccess()) {
        log.error("Email failed after {} attempts using provider: {}", 
                 result.getAttempts(), result.getProvider());
    }
} catch (EmailException e) {
    log.error("Email sending failed: {}", e.getMessage(), e);
    // Handle error (retry, alert, etc.)
}
```

## Best Practices

1. **Use Async for Non-Critical Emails**: Use async methods for welcome emails, notifications
2. **Validate Attachments**: Check file size and type before creating attachments
3. **Template Variables**: Always provide all required template variables
4. **Error Handling**: Implement proper error handling and logging
5. **Testing**: Use the provided test utilities for unit testing

## Monitoring

The service automatically tracks:
- Email send success/failure rates
- Provider performance
- Send duration metrics
- Retry attempts

Access metrics via Spring Boot Actuator endpoints.

## Example Service

See `EmailExampleService.java` for comprehensive usage examples including:
- Welcome emails
- Account statements with PDF attachments
- KYC document submissions
- Trading reports with multiple attachments
- Password reset emails
- Bulk notifications
