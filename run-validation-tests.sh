#!/bin/bash

echo "==================================="
echo "Running Validation Framework Tests"
echo "==================================="

echo ""
echo "1. Running Unit Tests (No Spring Context)..."
echo "-------------------------------------------"
mvn test -Dtest=GenericValidationUnitTest -q

if [ $? -eq 0 ]; then
    echo "✅ Unit tests passed!"
else
    echo "❌ Unit tests failed!"
    echo "Check the output above for details."
fi

echo ""
echo "2. Running Registry Tests..."
echo "----------------------------"
mvn test -Dtest=ValidationConfigurationRegistryTest -q

if [ $? -eq 0 ]; then
    echo "✅ Registry tests passed!"
else
    echo "❌ Registry tests failed!"
    echo "Check the output above for details."
fi

echo ""
echo "3. Running Integration Tests (With Spring Context)..."
echo "----------------------------------------------------"
mvn test -Dtest=GenericValidationTest -q

if [ $? -eq 0 ]; then
    echo "✅ Integration tests passed!"
else
    echo "❌ Integration tests failed!"
    echo "This might be due to context loading issues."
    echo "Try running the unit tests instead, or check the Spring configuration."
fi

echo ""
echo "==================================="
echo "Test Summary Complete"
echo "==================================="
echo ""
echo "If integration tests fail, you can:"
echo "1. Run unit tests only: mvn test -Dtest=GenericValidationUnitTest"
echo "2. Run registry tests only: mvn test -Dtest=ValidationConfigurationRegistryTest"
echo "3. Check application.properties for conflicting configurations"
echo "4. Ensure all validation framework beans are properly annotated"
